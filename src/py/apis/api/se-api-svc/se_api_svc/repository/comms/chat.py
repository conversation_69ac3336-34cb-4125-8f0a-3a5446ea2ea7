"""Chat is made up of Message and ChatEvent records.

ChatEvent records represent meta info such as users joining or leaving chat room.
Message records are actual messages.

User information may be incomplete. Only enhanced records will have "participants"
set with full names and contact details. Raw records will only have
"identifiers.fromId" and "identifiers.toIds".

Pagination is based on actual messages. Results are returned with ChatEvent records inserted between
Message records.
"""

import datetime as dt
from api_sdk.es_dsl.base import (
    And,
    ModelFilter,
    Not,
    NotExpiredFilter,
    Or,
    QueryString,
    SearchFeature,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import Nested, RangeFilter, SearchAfter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import ModelParams
from api_sdk.exceptions import NotFound
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import Pagination, Sort
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.utils.intervals import get_histogram_interval
from api_sdk.utils.utils import nested_dict_get
from elasticsearch_dsl import Q
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.repository.comms_surveillance.common import CustomClassProperty
from se_api_svc.schemas.comms.chat import (
    ChatEvent,
    ChatSummaryByCompanyItem,
    ChatSummaryByPeopleItem,
    ChatSummaryBySenderRecipient,
    Message,
)
from typing import Any, Dict, List, Optional, Union


def get_room_without_thread_messages(messages: List) -> List[Dict]:
    results = []

    message_sub_thread_map = {
        message.metadata["messageId"]: message.metadata.get("subThreadId", None)
        for message in messages
        if "messageId" in message.metadata.keys() and "subThreadId" in message.metadata.keys()
    }

    # Slack ingestion will not update subThreadId if the message
    # becomes a thread after the previous ingestion.
    # So we have to explicitly set subThreadId as messageId
    # if there's a thread message but the subThreadId is missing from first thread message.
    # See ENG-8215 for more details
    for message in messages:
        message_id = message.metadata.get("messageId", None)
        sub_thread_id = message.metadata.get("subThreadId", None)
        if not sub_thread_id and message_id in message_sub_thread_map.values():
            message.metadata["subThreadId"] = message_id

    for message in messages:
        if not nested_dict_get(message.metadata, "subThreadId") or nested_dict_get(
            message.metadata, "subThreadId"
        ) == nested_dict_get(message.metadata, "messageId"):
            results.append(message)
    return results


class ChatSearchBase(SearchModel):
    class Params(ModelParams):
        room_id: str = None
        start: Union[dt.datetime, dt.date] = None
        search_room_name: Optional[str] = None
        end: Union[dt.datetime, dt.date] = None
        f: Optional[str] = None
        companies: Optional[List[str]] = None
        except_companies: Optional[List[str]] = None
        people: Optional[List[str]] = None
        except_people: Optional[List[str]] = None
        search_after: List = None
        content: Optional[List[str]] = None
        source: Optional[str] = None
        sub_thread_id: Optional[str] = None
        room_ids: Optional[List[str]] = None
        behaviour_id: Optional[str] = None

        before_message_timestamp: Optional[Any] = None
        before_message_id: Optional[str] = None

    params: Params

    @CustomClassProperty
    def features(cls, instance):
        content_param_filters = []
        if instance and instance.params.content:
            # For each value in instance.params.content, add a query
            # that searches for the value in both
            # the "body.text" and "analytics.lexica.triggers.trigger" fields
            for val in instance.params.content:
                content_param_filters.extend(
                    [
                        QueryString(value=val, default_field="body.text"),
                        And(
                            Nested(
                                Nested(
                                    QueryString(
                                        value=val, default_field="analytics.lexica.triggers.trigger"
                                    ),
                                    path="analytics.lexica.triggers",
                                ),
                                path="analytics.lexica",
                            ),
                        ),
                    ]
                )

        return [
            NotExpiredFilter,
            TermFilter(name="roomId", param="room_id"),
            TermFilter(name="roomId", param="room_ids"),
            TermFilter(name="metadata.subThreadId", param="sub_thread_id"),
            TermFilter(name="metadata.source.client", param="source"),
            RangeFilter(field="timestamps.timestampStart"),
            FlangFilter.simple(
                param="f",
                nested_paths=["participants"],
            ),
            TermFilter(name="identifiers.fromId", param="people"),
            Not(TermFilter(name="identifiers.fromId", param="except_people")),
            Nested(
                TermFilter(name="participants.value.counterparty.name", param="companies"),
                path="participants",
            ),
            Nested(
                Not(
                    TermFilter(
                        name="participants.value.counterparty.name", param="except_companies"
                    )
                ),
                path="participants",
            ),
            Or(
                *content_param_filters,
            ),
            QueryString(param="search_room_name", default_field="roomName"),
        ]

    default_sort_order = [
        Sort(field="timestamps.timestampStart", order=Sort.Order.asc),
        Sort(field="&id", order=Sort.Order.asc),
    ]


@SearchFeature.register_q_builder
def BeforeMessageFilter(params):
    if params.before_message_timestamp and params.before_message_id:
        return Q(
            "range", **{"timestamps.timestampStart": {"lt": params.before_message_timestamp}}
        ) | (
            Q("term", **{"timestamps.timestampStart": params.before_message_timestamp})
            & Q("range", **{"&id": {"lt": params.before_message_id}})
        )


class MessageSearch(ChatSearchBase):
    @CustomClassProperty
    def features(cls, instance):
        params = instance.params.dict() if instance is not None else {}

        return ChatSearchBase(**params).features + [
            ModelFilter(model=Message),
            SearchAfter,
            BeforeMessageFilter,
        ]


class MessageLessFieldsSearch(MessageSearch):
    @property
    def extras(self):
        return [
            {
                "_source": {
                    "includes": [
                        "&*",
                        "timestamps",
                        "identifiers",
                        "body.text",
                        "hasAttachment",
                        "chatType",
                        "errorCode",
                        "folder",
                        "roomId",
                        "roomName",
                        "seen",
                        "metadata",
                        "&hash",
                    ],
                }
            }
        ]


class ChatEventSearch(ChatSearchBase):
    @CustomClassProperty
    def features(cls, instance):
        params = instance.params.dict() if instance is not None else {}

        return ChatSearchBase(**params).features + [
            ModelFilter(model=ChatEvent),
        ]


class ChatSummaryByDateInterval(MessageSearch):
    class Params(MessageSearch.Params):
        interval: str = "1d"

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        return {
            "TIMES": {
                "date_histogram": {
                    "field": "timestamps.timestampStart",
                    "fixed_interval": self.params.interval,
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                    "min_doc_count": 1,
                }
            }
        }


class ChatRepository(RequestBoundRepository):
    async def get_chat_events(self, with_total_count: bool = False, **search_params):
        chat_events = await self.get_many(
            search_model_cls=ChatEventSearch,
            **search_params,
        )
        if with_total_count:
            return chat_events.hits.total, chat_events.as_list()
        return chat_events.as_list()

    async def get_message(self, message_id: str) -> Message:
        return await self.get_one(Message, message_id)

    async def get_rooms(self, **search_params):
        rooms = []
        for item in (
            await self.get_aggs(
                search_model_cls=MessageSearch,
                aggs={
                    "ROOM_ID": {
                        "terms": {"field": "roomId", "size": ES_MAX_AGG_SIZE},
                        "aggs": {
                            "ROOM_NAME": {
                                "terms": {"field": "roomName", "size": ES_MAX_AGG_SIZE},
                            }
                        },
                    },
                },
                **search_params,
            )
        ).iter_raw_bucket_agg("ROOM_ID"):
            rooms.append(
                dict(
                    roomId=item["key"],
                    roomName=item["ROOM_NAME"]["buckets"][0]["key"]
                    if item["ROOM_NAME"]["buckets"]
                    else None,
                    count=item["doc_count"],
                )
            )
        return rooms

    async def get_room_summary_by_time(
        self,
        room_id: str,
        start=None,
        end=None,
        interval: str = None,
        **search_params,
    ):
        if not interval:
            if start and end:
                interval = f"{get_histogram_interval(start, end)}ms"
            else:
                interval = "1h"
        return list(
            (
                await self.get_aggs(
                    search_model_cls=ChatSummaryByDateInterval,
                    room_id=room_id,
                    interval=interval,
                    start=start,
                    end=end,
                    **search_params,
                )
            ).iter_bucket_agg("TIMES", key_str="key_as_string", key_as="datetime")
        )

    async def get_room_summary_by_sender(self, **search_params):
        return await self.get_room_summary_by_people(
            from_agg={"terms": {"field": "identifiers.fromId", "size": ES_MAX_AGG_SIZE}},
            **search_params,
        )

    async def get_room_summary_by_recipient(self, **search_params):
        return await self.get_room_summary_by_people(
            to_agg={"terms": {"field": "identifiers.toIds", "size": ES_MAX_AGG_SIZE}},
            **search_params,
        )

    async def get_room_summary_by_people_combined(self, **search_params):
        return await self.get_room_summary_by_people(
            from_agg={"terms": {"field": "identifiers.fromId", "size": ES_MAX_AGG_SIZE}},
            to_agg={"terms": {"field": "identifiers.toIds", "size": ES_MAX_AGG_SIZE}},
            **search_params,
        )

    async def get_room_summary_by_people(
        self,
        *,
        room_id: str,
        from_agg: Optional[Dict] = None,
        to_agg: Optional[Dict] = None,
        **search_params,
    ):
        aggs = {
            "PEOPLE": {
                "nested": {"path": "participants"},
                "aggs": {
                    "UNIQUE_IDS": {
                        "terms": {"field": "participants.value.uniqueIds", "size": ES_MAX_AGG_SIZE},
                        "aggs": {
                            "COMPANY_NAME": {
                                "terms": {
                                    "field": "participants.value.counterparty.name",
                                    "size": ES_MAX_AGG_SIZE,
                                }
                            },
                            "NAME": {
                                "terms": {
                                    "field": "participants.value.name",
                                    "size": ES_MAX_AGG_SIZE,
                                }
                            },
                        },
                    },
                },
            },
        }
        if from_agg:
            aggs["FROM_COUNTS"] = from_agg
        if to_agg:
            aggs["TO_COUNTS"] = to_agg
        result = await self.get_aggs(
            search_model_cls=MessageSearch,
            room_id=room_id,
            aggs=aggs,
            **search_params,
        )

        people = {}
        # The old way relies on Email address only...
        # attempt to use the Unique IDs and catch both Email and IM Ids
        for item in result.iter_raw_bucket_agg("PEOPLE.UNIQUE_IDS"):
            people[item["key"]] = {
                "email": item["key"],
                "company": item["COMPANY_NAME"]["buckets"][0]["key"]
                if item["COMPANY_NAME"]["buckets"]
                else None,
                "name": item["NAME"]["buckets"][0]["key"] if item["NAME"]["buckets"] else None,
            }

        counts = {"sender": [], "recipient": []}
        for agg_label, agg_name in [
            ("sender", "FROM_COUNTS"),
            ("recipient", "TO_COUNTS"),
        ]:
            agg_result = result.iter_raw_bucket_agg(agg_name) or []
            for item in agg_result:
                if item["key"] in people:
                    counts[agg_label].append(
                        ChatSummaryByPeopleItem(count=item["doc_count"], **people[item["key"]])
                    )
                else:
                    counts[agg_label].append(
                        ChatSummaryByPeopleItem(count=item["doc_count"], email=item["key"])
                    )
        return ChatSummaryBySenderRecipient(sender=counts["sender"], recipient=counts["recipient"])

    async def get_room_summary_by_company(self, room_id: str, **search_params):
        result = await self.get_aggs(
            search_model_cls=MessageSearch,
            room_id=room_id,
            aggs={
                "COMPANY": {
                    "nested": {"path": "participants"},
                    "aggs": {
                        "COMPANY_NAME": {
                            "terms": {
                                "field": "participants.value.counterparty.name",
                                "size": ES_MAX_AGG_SIZE,
                            },
                            "aggs": {
                                "WITH_NAME": {
                                    "reverse_nested": {},
                                }
                            },
                        }
                    },
                }
            },
            **search_params,
        )
        companies = []
        for item in result.aggregations["COMPANY"]["COMPANY_NAME"]["buckets"]:
            companies.append(
                ChatSummaryByCompanyItem(
                    count=item["WITH_NAME"]["doc_count"],
                    company=item["key"],
                )
            )
        return companies

    async def get_room_messages(self, room_id: str, minimal: bool = False, **search_params):
        search_model_cls = MessageLessFieldsSearch if minimal else MessageSearch
        return await self.get_many(
            search_model_cls=search_model_cls,
            room_id=room_id,
            **search_params,
        )

    async def get_room_first_message(self, room_id: str) -> Message:
        return (
            await self.get_room_messages(room_id=room_id, pagination=Pagination(take=1))
        ).as_list()[0]

    async def get_num_messages(self, **search_params) -> int:
        return await self.get_count(
            search_model=MessageSearch(**search_params), index_suffix=Message.Config.index_suffix
        )

    async def get_all_room_info(self, source: str, **params) -> List:
        search_room_name = params.get("model_qs", None)
        if search_room_name:
            params.pop("model_qs")

        # Applying search filter to get roomId
        # Since a single chat-room could contain
        # multiple roomName (See focus comment :- https://steeleye.atlassian.net/browse/ENG-941?focusedCommentId=112156)
        room_ids = []
        room_ids_results = await self.get_aggs(
            search_model_cls=MessageSearch,
            source=source,
            aggs={
                "room_id": {
                    "terms": {"field": "roomId"},
                }
            },
            search_room_name=search_room_name,
        )
        for room_id in room_ids_results.iter_raw_bucket_agg("room_id"):
            room_ids.append(room_id.get("key"))

        results = await self.get_aggs(
            search_model_cls=MessageSearch,
            room_ids=room_ids,
            aggs={
                "room_id": {
                    "terms": {"field": "roomId"},
                    "aggs": {
                        "room_names": {
                            "terms": {"field": "roomName", "order": {"message_agg": "desc"}},
                            "aggs": {
                                "message_agg": {
                                    "filter": {"term": {"&model": "Message"}},
                                    "aggs": {
                                        "message_id": {
                                            "terms": {
                                                "field": "&id",
                                                "size": 1,
                                                "order": {"timestamp_start": "desc"},
                                            },
                                            "aggs": {
                                                "timestamp_start": {
                                                    "sum": {"field": "timestamps.timestampStart"}
                                                }
                                            },
                                        }
                                    },
                                },
                            },
                        },
                        "sum": {"sum_bucket": {"buckets_path": "room_names>_count"}},
                    },
                }
            },
        )

        room_info = []

        for room_id in results.iter_raw_bucket_agg("room_id"):
            room_names_bucket = nested_dict_get(room_id, "room_names.buckets")
            message_id_bucket = {}

            for counter, room_name in enumerate(room_names_bucket):
                if not counter:
                    message_id_bucket = nested_dict_get(
                        room_name, "message_agg.message_id.buckets"
                    )[0]

                room_info.append(
                    {
                        "id": nested_dict_get(room_id, "key"),
                        "name": nested_dict_get(room_name, "key"),
                        "count": int(nested_dict_get(room_id, "sum.value")),
                        "message_id": nested_dict_get(message_id_bucket, "key"),
                    }
                )

        return room_info

    async def get_thread_messages(self, sub_thread_id: str, **kwargs) -> RawResult:
        results = await self.get_many(
            search_model_cls=MessageSearch, sub_thread_id=sub_thread_id, **kwargs
        )

        # pop out the first message and decrease count as
        # it would be the main thread message
        # TODO:
        #   If somehow the first message is not main thread message
        #   Iterate over all the message pop the message where
        #   subThreadId == messageId and decrease the count.
        if results.hits.hits:
            results.hits.hits.pop(0)
            results.hits.total -= 1
            return results

        raise NotFound(f"Thread with subThreadId {sub_thread_id} was not found")
