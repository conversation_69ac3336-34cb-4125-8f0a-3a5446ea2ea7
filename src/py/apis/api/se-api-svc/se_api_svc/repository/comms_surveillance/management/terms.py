# type: ignore
import inspect
import logging
from api_sdk.auth import Tenancy
from api_sdk.exceptions import BackendError, BadInput
from collections import defaultdict
from fastapi import HTTPException
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.repository.comms_surveillance.management.behaviours import BehavioursRepo
from se_api_svc.repository.comms_surveillance.management.databases import BaseDBManager, ModelType
from se_api_svc.repository.comms_surveillance.management.sub_behaviours import SubBehavioursRepo
from se_api_svc.schemas.surveillance.comms_surveillance.lexica.terms import (
    TermCreate,
    TermResponse,
    TermsPaginatedResponse,
    TermUpdate,
)
from se_db_utils.database import Database
from sqlalchemy import and_
from sqlalchemy.orm.session import Session
from tenant_db.models.lexica.behaviours import Behaviour
from tenant_db.models.lexica.sub_behaviours import SubBehaviour
from tenant_db.models.lexica.terms import Term
from typing import List, Optional


class TermsRepo(BaseDBManager):
    model = Term

    def __init__(
        self,
        tenancy: Tenancy,
        db: Database,
        config: ApiServiceConfig,
        tenant_schema: str = None,
    ) -> None:
        self.tenant_schema = tenant_schema if tenant_schema else tenancy.tenant
        self.config = config
        super().__init__(tenancy, db, self.tenant_schema)

    def get_term_with_se_lexica(self, id: str, session: Session):
        term = super().get(id, session=session)

        # To check if term existing or not
        # If not the api should throw a 404 instead of 500
        if not term:
            return None, None

        try:
            return (
                session.query(self.model, Behaviour.fromSeLexica)
                .filter(self.model.tenant == self.tenancy.tenant, self.model.id == id)
                .join(SubBehaviour, and_(self.model.subBehaviourId == SubBehaviour.id))
                .join(
                    Behaviour,
                    and_(SubBehaviour.behaviourId == Behaviour.id),
                )
                .first()
            )
        except Exception as e:
            session.rollback()
            logging.error(e)

    def get_terms_with_se_lexica(
        self, sort: str = None, take: int = 50, skip: int = 0, filters: dict = {}
    ):
        """Fetches the list of terms along with fromSeLexica attribute from its
        corresponding record in Behaviour table."""
        with self.get_session() as session:
            try:
                terms_query = session.query(self.model).filter(
                    self.model.tenant == self.tenancy.tenant
                )
                terms_query = self.filter(query=terms_query, filters=filters)
                terms_query = terms_query.cte(name="terms_query")

                query = (
                    session.query(terms_query, Behaviour.fromSeLexica)
                    .join(SubBehaviour, and_(SubBehaviour.id == terms_query.c.subBehaviourId))
                    .join(Behaviour, and_(Behaviour.id == SubBehaviour.behaviourId))
                ).distinct()

                query = self.sort(query=query, sort=sort)
                query, headers = self.paginate(query, take=take, skip=skip)
                results = query.all()
                return TermsPaginatedResponse(header=headers, results=results)

            except Exception as e:
                session.rollback()
                logging.error(f"Error while fetching terms: {e}")

    def get(
        self,
        id: str,
        id_filed_name: str = "id",
        as_dict: bool = False,
        with_lexica_type: bool = False,
        session: Session = None,
    ) -> TermResponse:
        close_session = False
        if not session:
            session = self.get_session()
            close_session = True
        term, from_se_lexica = self.get_term_with_se_lexica(id=id, session=session) or (None, None)
        if close_session:
            session.close()
        if not term:
            raise HTTPException(status_code=404, detail=f"Term with ID '{id}' not found")
        if term.tenant != self.tenancy.tenant:
            raise HTTPException(status_code=403, detail="Not Authorized")
        if as_dict:
            term_dict = term.__dict__
            term_dict["fromSeLexica"] = from_se_lexica
            return term_dict

        if with_lexica_type:
            return term, from_se_lexica

        return term

    def get_all(
        self, sort: str = None, take: int = 50, skip: int = 0, filters: dict = {}
    ) -> TermsPaginatedResponse:
        results, headers = super().get_all(
            self.model, sort=sort, take=take, skip=skip, filters=filters
        )
        return TermsPaginatedResponse(header=headers, results=results)

    def create(self, term_in: TermCreate) -> TermResponse:
        sub_behaviour_repo = SubBehavioursRepo(tenancy=self.tenancy, db=self.db)
        with self.get_session() as session:
            sub_behaviour = sub_behaviour_repo.get(id=str(term_in.subBehaviourId), session=session)
            if sub_behaviour.tenant != self.tenancy.tenant:
                raise HTTPException(
                    status_code=403,
                    detail="Sub_behaviour_id is not correct.",
                )
            term = self.model(**term_in.dict())
            term.tenant = self.tenancy.tenant
            term.createdBy = self.tenancy.userId
            term.sourceKey = "API"
            if self.if_exists(
                self.model,
                criteria={
                    "term": term.term,
                    "termBase": term.termBase,
                    "termPaired": term.termPaired,
                    "subBehaviourId": term.subBehaviourId,
                    "language": term.language,
                },
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"Term with name '{term.term}' "
                    f"and base_term '{term.termBase}' already exists",
                )
            if term.parentId:
                if not super().get(id=term.parentId):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Parent term with id '{term.parentId}' does not exists",
                    )
            try:
                session.add(term)
                session.commit()
                session.refresh(term)
                self.update_parent_attr(term, session=session)
                behaviour_name = getattr(
                    self.get_behaviour_from_sub_behaviour(str(term.subBehaviourId)),
                    "behaviourName",
                    None,
                )
                logging.info(
                    f"LexicaManagement: Lexica term '{term.term}' (ID: {term.id}) created "
                    f"by user {self.tenancy.user_name} for behaviour '{behaviour_name}'"
                )
                return TermResponse(**self.to_dict(term))
            except Exception as e:
                session.rollback()
                logging.error(e)

    def create_bulk(self, terms_in: List[TermCreate]):
        sub_behaviour_repo = SubBehavioursRepo(tenancy=self.tenancy, db=self.db)
        with self.get_session() as session:
            sub_behaviour = sub_behaviour_repo.get(
                id=str(terms_in[0].subBehaviourId), session=session
            )
            if sub_behaviour.tenant != self.tenancy.tenant:
                raise HTTPException(
                    status_code=403,
                    detail="Sub_behaviour_id is not correct.",
                )
            try:
                created_terms, errors = [], []
                for term in terms_in:
                    term = self.model(**term.dict())
                    term.tenant = self.tenancy.tenant
                    term.createdBy = self.tenancy.userId
                    term.sourceKey = "API"
                    if self.if_exists(
                        self.model,
                        criteria={
                            "term": term.term,
                            "termBase": term.termBase,
                            "termPaired": term.termPaired,
                            "subBehaviourId": term.subBehaviourId,
                            "language": term.language,
                        },
                    ):
                        errors.append(
                            {
                                "statusCode": 400,
                                "message": f"Term with name '{term.term}' "
                                f"and base_term '{term.termBase}' already exists",
                            }
                        )
                        continue
                    if term.parentId:
                        if not super().get(id=term.parentId):
                            errors.append(
                                {
                                    "statusCode": 400,
                                    "message": f"Parent term with id '{term.parentId}' does not exists",  # noqa: E501
                                }
                            )
                            continue
                    session.add(term)
                    created_terms.append(term)

                session.commit()  # Commit all terms in bulk
                # Refresh each term and update parent attributes
                for term in created_terms:
                    session.refresh(term)
                    self.update_parent_attr(term, session=session)
                logging.info(
                    f"LexicaManagement: Lexica terms created by user {self.tenancy.user_name} - "
                    f"{len(created_terms)} terms created"
                )
                return [TermResponse(**self.to_dict(term)) for term in created_terms], errors
            except Exception as e:
                session.rollback()
                logging.error(e)

    def update(
        self,
        id: str,
        term_in: TermUpdate,
        session: Session = None,
        check_lexica_validation: bool = False,
        with_changes: Optional[bool] = False,
    ) -> TermResponse:
        db_session = session if session else self.get_session()
        with db_session as session:
            term, from_se_lexica = self.get(id=id, with_lexica_type=True, session=session)
            self.raise_if_term_is_expiry(term)
            self.validate_term(term, term_in, from_se_lexica, check_lexica_validation)
            term.edited = True
            self.update_parent_attr(term=term, session=session)
            term_obj = super().update(
                db_obj=term, obj_in=term_in, with_changes=with_changes, session=session
            )
            behaviour_name = getattr(
                self.get_behaviour_from_sub_behaviour(str(term.subBehaviourId)),
                "behaviourName",
                None,
            )
            if term_obj:
                logging.info(
                    f"LexicaManagement: Lexica term '{term.term}' (ID: {term.id}) updated "
                    f"by user {self.tenancy.user_name} for behaviour '{behaviour_name}'"
                )
            if with_changes:
                term_obj, changes = term_obj
                return {
                    "fromSeLexica": from_se_lexica,
                    **(self.to_dict(term_obj) if term_obj else {}),
                }, changes
            return {"fromSeLexica": from_se_lexica, **(self.to_dict(term_obj) if term_obj else {})}

    def delete(self, id: str, session=None):
        db_session = session if session else self.get_session()
        with db_session as session:
            term, from_se_lexica = self.get(id=id, with_lexica_type=True, session=session)
            behaviour_name = getattr(
                self.get_behaviour_from_sub_behaviour(str(term.subBehaviourId)),
                "behaviourName",
                None,
            )
            self.raise_if_term_is_expiry(term)
            self.update_parent_attr(term, session=session)
            result = super().delete(db_obj=term, session=session)
            if result:
                logging.info(
                    f"LexicaManagement: Lexica term '{term.term}' (ID: {term.id}) deleted "
                    f"by user {self.tenancy.user_name} for behaviour '{behaviour_name}'"
                )
            return {
                "fromSeLexica": from_se_lexica,
                **(self.to_dict(result) if result else {}),
            }

    @staticmethod
    def update_object(obj, data: dict):
        for key, value in data.items():
            setattr(obj, key, value)

    def bulk_update(self, term_ids: List[str], se_lexica=None, session=None, **params):
        db_session = session if session else self.get_session()
        behaviour_sub_filters = [SubBehaviour.behaviourId == Behaviour.id]
        if se_lexica is not None:
            behaviour_sub_filters.append(Behaviour.fromSeLexica == se_lexica)
        with db_session as session:
            updated_terms_map = defaultdict(list)
            try:
                # Todo: remove when lexica V8 deployed to production
                if self.config.HIDE_LEXICA_UPGRADES:
                    terms = (
                        session.query(self.model, Behaviour.fromSeLexica, Behaviour.behaviourName)
                        .filter(
                            self.model.tenant == self.tenancy.tenant,
                            self.model.id.in_(term_ids),
                        )
                        .join(SubBehaviour, and_(self.model.subBehaviourId == SubBehaviour.id))
                        .join(Behaviour, and_(*behaviour_sub_filters))
                        .all()
                    )
                else:
                    terms = (
                        session.query(self.model, Behaviour.fromSeLexica, Behaviour.behaviourName)
                        .filter(
                            self.model.tenant == self.tenancy.tenant,
                            self.model.id.in_(term_ids),
                            self.model.expiry.is_(None),
                        )
                        .join(SubBehaviour, and_(self.model.subBehaviourId == SubBehaviour.id))
                        .join(Behaviour, and_(*behaviour_sub_filters))
                        .all()
                    )

                for term, from_se_lexica, behaviour_name in terms:
                    self.update_parent_attr(term, session=session)
                    self.update_object(term, params)
                    updated_terms_map[behaviour_name].append(
                        {
                            **self.to_dict(self.add_update_attr(term)),
                            "from_se_lexica": from_se_lexica,
                        }
                    )
            except HTTPException as e:
                session.rollback()
                raise e

            session.commit()
            action = "deleted" if params.get("retired") else "restored"
            logging.info(
                f"LexicaManagement: Lexica terms {action} by user {self.tenancy.user_name} - "
                f"{len(terms)} terms {action}"
            )
            return updated_terms_map

    def undelete(self, id: str) -> TermResponse:
        with self.get_session() as session:
            term, from_se_lexica = self.get(id=id, with_lexica_type=True, session=session)
            behaviour_name = getattr(
                self.get_behaviour_from_sub_behaviour(str(term.subBehaviourId)),
                "behaviourName",
                None,
            )
            self.raise_if_term_is_expiry(term)
            self.update_parent_attr(term, session=session)
            result = super().undelete(db_obj=term, session=session)
            if result:
                logging.info(
                    f"LexicaManagement: Lexica term '{term.term}' (ID: {term.id}) restored "
                    f"by user {self.tenancy.user_name} for behaviour '{behaviour_name}'"
                )
            return {
                "fromSeLexica": from_se_lexica,
                **self.to_dict(super().undelete(db_obj=term, session=session)),
            }

    def update_parent_attr(self, term: ModelType, session: Session) -> None:
        sub_behaviour = SubBehavioursRepo(tenancy=self.tenancy, db=self.db).get(
            id=term.subBehaviourId, session=session
        )
        behaviour = BehavioursRepo(tenancy=self.tenancy, db=self.db).get(
            id=sub_behaviour.behaviourId, session=session
        )
        self.add_update_attr(sub_behaviour)
        self.add_update_attr(behaviour)

    @staticmethod
    def validate_term(
        term: Term,
        term_in: TermUpdate,
        from_se_lexica: bool = False,
        check_lexica_validation: bool = False,
    ):
        current_search_type = getattr(term_in, "exclusionSearchType") or term.exclusionSearchType

        if (
            from_se_lexica
            and check_lexica_validation
            and (current_search_type != term.exclusionSearchType)
        ):
            raise BadInput("SteelEye lexica term operator can't be edited")

        if term.termOperatorsFuzziness and term.termOperatorsFuzziness > 0:
            if len(term.term) > 14 and term.language in ["ja.kj", "zh.cn", "zh.tw"]:
                raise BadInput(
                    "Length of term shouldn't be greater than "
                    "14 for following languages ['ja.kj', 'zh.cn', 'zh.tw']"
                )
            elif len(term.term.split(" ")) > 7:
                raise BadInput("Word count of term should not be greater than 7")

    def get_behaviour_from_sub_behaviour(self, sub_behaviour_id: str):
        with self.get_session() as session:
            try:
                query = ""
                query = (
                    session.query(Behaviour)
                    .join(
                        SubBehaviour,
                        and_(
                            SubBehaviour.behaviourId == Behaviour.id,
                            SubBehaviour.tenant == Behaviour.tenant,
                        ),
                    )
                    .filter(
                        SubBehaviour.tenant == self.tenancy.tenant,
                        SubBehaviour.id == sub_behaviour_id,
                    )
                )

                return query.first()
            except Exception as e:
                session.rollback()
                logging.error(
                    f"Method: {inspect.currentframe().f_code.co_name}; "
                    f"Query: {str(query)}; Error: {e}"
                )

    def raise_if_term_is_expiry(self, term):
        # Not allowing user to update an expiry term
        # Todo: remove when lexica V8 deployed to production
        if self.config.HIDE_LEXICA_UPGRADES:
            return
        if term.expiry:
            raise BackendError("Error updating term")
