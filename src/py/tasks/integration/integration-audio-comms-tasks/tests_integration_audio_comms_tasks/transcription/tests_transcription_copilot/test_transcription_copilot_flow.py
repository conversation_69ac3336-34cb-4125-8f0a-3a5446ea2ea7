# type: ignore
# flake8: noqa: E402
import copy
import json
import os
import pandas as pd
import pytest
import tempfile
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from copilot_utils.static import OpenAIResponse
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables
from openai.types import CompletionUsage
from openai.types.chat import ChatCompletion, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice
from pathlib import Path
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.elastic_search import EsActionEnum
from shutil import rmtree
from typing import List
from unittest import mock

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
INPUT_NDJSON_TRANSCRIPT = (
    f"{CURRENT_PATH}/data/valid_executions/scenario_1/sample_transcript.ndjson"
)
INPUT_NDJSON_CALL = f"{CURRENT_PATH}/data/valid_executions/scenario_1/sample_call.ndjson"
TEMP_FILE_TRANSCRIPT = tempfile.NamedTemporaryFile(suffix="__transcript__.ndjson")
TEMP_FILE_CALL = tempfile.NamedTemporaryFile(suffix="__call__.ndjson")

EXPECTED_TRANSCRIPT_OUTPUT_NDJSON = (
    f"{CURRENT_PATH}/data/valid_executions/scenario_1/expected_output_transcript.ndjson"
)
EXPECTED_CALL_OUTPUT_NDJSON = (
    f"{CURRENT_PATH}/data/valid_executions/scenario_1/expected_output_call.ndjson"
)
SERIALIZER_TMP_DIR = CURRENT_PATH.joinpath("serializer_tmp_dir")
SERIALIZER_TMP_DIR.mkdir(parents=True, exist_ok=True)
DEFAULT_OPENAI_VALUES = {
    "OPENAI_API_BASE": "",
    "OPENAI_API_KEY": "",
    "OPENAI_API_MODEL": "",
    "OPENAI_API_VERSION": "2023-03-15-preview",
}

os.environ[IntegrationAriesTaskVariables.SERIALIZER_TMP_DIR] = SERIALIZER_TMP_DIR.as_posix()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(SERIALIZER_TMP_DIR)
        CURRENT_PATH.joinpath("result.json").unlink(missing_ok=True)
        CURRENT_PATH.joinpath("batch_0.csv").unlink(missing_ok=True)

    request.addfinalizer(_end)


def mock_es(mocker, module, scroll_result: pd.DataFrame):
    # Mocks for ElasticSearch
    mock_get_es_config = mocker.patch.object(module, "get_es_config")
    mock_get_es_config.return_value = ResourceConfig(
        host="localhost",
        port="9200",
        scheme="http",
        meta_prefix=MetaPrefix.AMPERSAND,
    )

    mock_es_repo = mocker.patch.object(module, "get_repository_by_cluster_version")
    es_obj = mock_es_repo.return_value
    es_obj.scroll.return_value = scroll_result

    es_obj.MAX_TERMS_SIZE = 1024
    es_obj.meta.prefix = MetaPrefix.AMPERSAND
    es_obj.meta.key = "&key"
    es_obj.meta.id = "&id"
    es_obj.meta.model = "&model"
    es_obj.meta.hash = "&hash"


@pytest.fixture
def setup_env():
    env_vars = {
        **DEFAULT_OPENAI_VALUES,
        "STACK": "dev-shared-2",
        "DATA_PLATFORM_CONFIG_API_URL": "https://config-api.nonprod-eu-ie-1.steeleye.co",
    }

    with mock.patch.dict(os.environ, env_vars):
        yield


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_transcription_copilot_1",
        name="cloud9_voice",
        stack="dev-shared-2",
        tenant="irises8",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params={
            "Transcript": {
                "params": {
                    "file_uri": "s3://irises8.dev.steeleye.co/aries/ingest/sample_transcript"
                    ".ndjson",
                    "es_action": "create",
                    "data_model": "se_elastic_schema.models.tenant.communication.transcript:Transcript",  # noqa: E501
                }
            },
            "Call": {
                "params": {
                    "file_uri": "s3://irises8.dev.steeleye.co/aries/ingest/sample_call.ndjson",
                    "es_action": "update",
                    "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
                }
            },
            "tenant_config_feature_flags": ["azure-processing"],
        }
    )
    task = TaskFieldSet(name="test_transcription_copilot", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def mock_gpt_response():
    with open(DATA_PATH.joinpath("valid_executions/scenario_1/mock_completion_1.json")) as f:
        content_1 = json.dumps(json.load(f))
    with open(DATA_PATH.joinpath("valid_executions/scenario_1/mock_completion_2.json")) as f:
        content_2 = json.dumps(json.load(f))

    openai_response_template_1 = ChatCompletion(
        id="abc",
        created=1727699717,
        model="sample_model",
        object="chat.completion",
        usage=CompletionUsage(
            total_tokens=100,
            # Leaving these as zero as they are not relevant to the tests
            prompt_tokens=0,
            completion_tokens=0,
        ),
        choices=[
            Choice(
                message=ChatCompletionMessage(content=content_1, role="assistant"),
                index=0,
                finish_reason="stop",
            )
        ],
    )
    openai_response_template_2 = copy.deepcopy(openai_response_template_1)
    openai_response_template_2.choices[0].message.content = content_2
    return [
        OpenAIResponse(index=1, response=openai_response_template_1),
        OpenAIResponse(index=2, response=openai_response_template_2),
    ]


class TestTranscriptionCopilotFlow:
    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    def test_valid_executions(
        self, mocker, sample_aries_task_input: AriesTaskInput, mock_gpt_response, setup_env
    ):
        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_aries_task_input,
            mock_gpt_response=mock_gpt_response,
        )

        expected_transcript_df = pd.read_json(EXPECTED_TRANSCRIPT_OUTPUT_NDJSON, lines=True)
        expected_call_df = pd.read_json(EXPECTED_CALL_OUTPUT_NDJSON, lines=True)
        final_transcript_df = pd.read_json(TEMP_FILE_TRANSCRIPT.name, lines=True)
        final_call_df = pd.read_json(TEMP_FILE_CALL.name, lines=True)
        pd.testing.assert_frame_equal(
            final_call_df.sort_index(axis=1),
            expected_call_df.sort_index(axis=1),
        )
        pd.testing.assert_frame_equal(
            final_transcript_df.sort_index(axis=1),
            expected_transcript_df.sort_index(axis=1),
        )
        assert aries_task_result.output_param.params["Transcript"]["params"] == {
            "file_uri": TEMP_FILE_TRANSCRIPT.name,
            "es_action": EsActionEnum.INDEX.value,
            "data_model": "se_elastic_schema.models.tenant.communication.transcript:Transcript",
        }
        assert aries_task_result.output_param.params["Call"]["params"] == {
            "file_uri": TEMP_FILE_CALL.name,
            "es_action": EsActionEnum.UPDATE.value,
            "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
        }

    @staticmethod
    def _run_aries_task(
        mocker, sample_aries_task_input: AriesTaskInput, mock_gpt_response: List[dict]
    ):
        from copilot_utils.client import SeOpenApiClient
        from integration_audio_comms_tasks.transcription.transcription_copilot import (
            transcription_copilot_flow,
            transcription_copilot_task,
        )
        # Mock Elastic connections across multiple tasks

        mock_file = mocker.patch.object(transcription_copilot_flow, "run_download_file")
        mock_file.side_effect = [INPUT_NDJSON_TRANSCRIPT, INPUT_NDJSON_CALL]

        mock_output_destination = mocker.patch.object(
            transcription_copilot_flow, "create_ndjson_path"
        )
        mock_output_destination.side_effect = [TEMP_FILE_TRANSCRIPT.name, TEMP_FILE_CALL.name]

        mock_auditor_upload = mocker.patch(
            "integration_wrapper.integration_aries_task.upload_audit"
        )
        mock_auditor_upload.return_value = None

        # mock ai completions
        mock_completion = mocker.patch.object(
            SeOpenApiClient,
            "call_open_ai",
        )
        mock_completion.return_value = mock_gpt_response

        # Run flow
        return transcription_copilot_task.transcription_copilot_run(
            aries_task_input=sample_aries_task_input
        )
