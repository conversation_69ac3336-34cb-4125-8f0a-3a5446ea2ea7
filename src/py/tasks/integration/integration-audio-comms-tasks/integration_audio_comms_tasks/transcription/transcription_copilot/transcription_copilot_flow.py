# type: ignore
import logging
import pandas as pd
from addict import Dict
from api_sdk.auth import tenant_from_realm
from aries_config_api_compatible_client.tenant_workflow import CompatibleTenantWorkflowAPIClient
from aries_se_api_client.client import AriesApiClient
from aries_se_comms_tasks.transcription.static import TranscriptModelFields
from aries_se_comms_tasks.transcription.transcript_copilot.copilot_call_frame_with_transcript_fields import (  # noqa E501
    run_copilot_call_frame_with_transcript_fields,
)
from aries_se_comms_tasks.transcription.transcript_copilot.transcript_copilot import (
    run_transcript_copilot,
)
from aries_se_comms_tasks.voice.static import CallColumns, SemanticModelFields
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.datetime.get_seconds_from_time import Params as GetSecondsFromTimeParams
from aries_se_core_tasks.datetime.get_seconds_from_time import run_get_seconds_from_time
from aries_se_core_tasks.frame.filter_columns import Params as ParamsFilterColumns
from aries_se_core_tasks.frame.filter_columns import run_filter_columns
from aries_se_core_tasks.frame.frame_concatenator import Params as ParamsFrameConcatenator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from data_platform_config_api_client.tenant_workflow import TenantWorkflowAPI
from integration_audio_comms_tasks.transcription.transcription_copilot.input_schema import (
    TranscriptionCopilotAriesTaskInput,
)
from integration_audio_comms_tasks.transcription.transcription_copilot.static import (
    TranscriptionTempColumns,
)
from omegaconf import OmegaConf
from pathlib import Path
from se_core_tasks.frame.filter_columns import ActionEnum
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_elastic_schema.static.tenant_configuration import FeatureFlags
from se_enums.elastic_search import EsActionEnum
from se_io_utils.json_utils import ndjson_to_flat_dataframe

logger = logging.getLogger(__name__)


@serializer
def deduplicate_columns(df, positional_keep="first"):
    return df.loc[:, ~df.columns.duplicated(keep=positional_keep)]


def filter_transcripts_by_duration(
    call_frame: pd.DataFrame,
    transcript_frame: pd.DataFrame,
    cloud_provider_prefix: str,
    duration_threshold_seconds: int,
) -> pd.DataFrame:
    """
    Filter both call and transcript frames together based on call duration threshold.
    Uses the same mask for both frames to ensure perfect consistency.
    """
    required_call_columns = [
        CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET,
        CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY,
        CallColumns.CALL_DURATION,
    ]

    if not all(col in call_frame.columns for col in required_call_columns):
        logger.warning(
            f"Required call columns {required_call_columns} not found. Skipping duration filtering."
        )
        return call_frame, transcript_frame

    duration_not_null_mask = call_frame[CallColumns.CALL_DURATION].notnull()
    if not duration_not_null_mask.any():
        logger.warning("All call durations are null. Skipping duration filtering.")
        return call_frame, transcript_frame

    required_call_df = call_frame.loc[duration_not_null_mask, required_call_columns]
    required_call_df.loc[:, TranscriptionTempColumns.RECORDING_SOURCE_KEY] = (
        cloud_provider_prefix
        + required_call_df.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET]
        + "/"
        + required_call_df.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY]
    )

    call_frame_with_seconds = run_get_seconds_from_time(
        source_frame=required_call_df,
        params=GetSecondsFromTimeParams(
            source_time_attribute=CallColumns.CALL_DURATION,
            target_attribute=TranscriptionTempColumns.CALL_DURATION_IN_SECONDS,
            source_time_format="%H:%M:%S",
        ),
        skip_serializer=True,
    )

    duration_seconds_col = call_frame_with_seconds[
        TranscriptionTempColumns.CALL_DURATION_IN_SECONDS
    ]

    duration_seconds_col = pd.to_numeric(duration_seconds_col, errors="coerce")

    duration_gte_threshold_mask = duration_seconds_col.notnull() & (
        duration_seconds_col >= duration_threshold_seconds
    )

    valid_recording_source_keys = required_call_df.loc[
        duration_gte_threshold_mask, TranscriptionTempColumns.RECORDING_SOURCE_KEY
    ].tolist()

    transcript_frame[TranscriptionTempColumns.SKIP_COPILOT] = True

    transcript_mask = transcript_frame[TranscriptModelFields.RECORDING_SOURCE_KEY].isin(
        valid_recording_source_keys
    )
    transcript_frame.loc[transcript_mask, TranscriptionTempColumns.SKIP_COPILOT] = False

    return transcript_frame


def transcription_copilot_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str = "result.json",
):
    # SETUP #

    # Parse and validate AriesTaskInput parameters
    transcription_copilot_flow_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=TranscriptionCopilotAriesTaskInput
    )
    transcript_source_file_path = transcription_copilot_flow_input.Transcript["params"]["file_uri"]
    call_source_file_path = transcription_copilot_flow_input.Call["params"]["file_uri"]

    realm: str = get_bucket(file_uri=transcript_source_file_path)
    tenant: str = tenant_from_realm(realm=realm)

    cloud_provider = get_cloud_provider_from_file_uri(file_uri=call_source_file_path)
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    if (
        FeatureFlags.AZURE_PROCESSING.value
        not in transcription_copilot_flow_input.tenant_config_feature_flags
    ):
        logger.warning(f"Tenant:{tenant} not eligible for copilot, Finishing flow")
        transcript_output = add_nested_params(
            file_uri=transcript_source_file_path,
            es_action=EsActionEnum.CREATE,
            data_model="se_elastic_schema.models.tenant.communication.transcript:Transcript",
        )
        call_output = add_nested_params(
            file_uri=call_source_file_path,
            es_action=EsActionEnum.UPDATE,
            data_model="se_elastic_schema.models.tenant.communication.call:Call",
        )

        finish_flow(
            result_path=result_path,
            result_data={
                MetaModel.TRANSCRIPT: transcript_output,
                MetaModel.CALL: call_output,
            },
        )
        return

    # Download remote files from cloud
    transcript_local_file_path = run_download_file(file_url=transcript_source_file_path)
    call_local_file_path = run_download_file(file_url=call_source_file_path)

    # Determine the cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=Dict(file_uri=transcript_source_file_path),
        cloud_provider_prefix=cloud_provider_prefix,
    )
    # Load the file containing configs and initialize vault client
    config = OmegaConf.load(Path(__file__).parent.joinpath("config.yml"))

    # read source data for transcript and call into separate dataframes
    transcript_source_frame = ndjson_to_flat_dataframe(
        ndjson_file_path=transcript_local_file_path,
        skip_on_emtpy_frame=False,
    )
    call_source_frame = ndjson_to_flat_dataframe(
        ndjson_file_path=call_local_file_path,
        skip_on_emtpy_frame=False,
    )
    workflow_name = aries_task_input.workflow.name
    # get tenant workflow configuration for destination workflow
    config_api_client = AriesApiClient(host=config.data_platform_config_api_url)
    tenant_workflow_api = TenantWorkflowAPI(config_api_client)
    transform_workflow_tw_config = CompatibleTenantWorkflowAPIClient.get(
        tenant_workflow_api=tenant_workflow_api, tenant_name=tenant, workflow_name=workflow_name
    )

    # END SETUP #

    # BUSINESS LOGIC #
    try:
        duration_threshold_seconds = transform_workflow_tw_config.static_config[
            "copilot_duration_threshold"
        ]
    except KeyError:
        logger.warning(
            "Could not find copilot duration threshold in tenant config. "
            "Skipping duration filtering."
        )
        duration_threshold_seconds = 0

    # Filter out calls and transcripts with duration less than threshold duration together
    transcript_source_frame_filtered = filter_transcripts_by_duration(
        call_frame=call_source_frame,
        transcript_frame=transcript_source_frame,
        cloud_provider_prefix=cloud_provider_prefix,
        duration_threshold_seconds=duration_threshold_seconds,
    )

    # Initialize final frames with filtered data
    transcript_final_frame = transcript_source_frame
    call_final_frame = call_source_frame

    # Only run copilot processing if we have valid data after filtering
    if not transcript_source_frame.empty and not call_source_frame.empty:
        logger.info("Running transcript copilot on filtered data")
        copilot_eligible_transcripts = transcript_source_frame_filtered[
            transcript_source_frame_filtered[TranscriptionTempColumns.SKIP_COPILOT] == False  # noqa: E712
        ]
        skipped_copilot_df = transcript_source_frame_filtered[
            transcript_source_frame_filtered[TranscriptionTempColumns.SKIP_COPILOT] == True  # noqa: E712
        ]

        if not copilot_eligible_transcripts.empty:
            # Run Transcript Copilot task only on eligible transcripts
            transcript_copilot_result = run_transcript_copilot(
                source_frame=copilot_eligible_transcripts,
                config=config,
                tenant=tenant,
                workflow=aries_task_input.workflow.name,
            )

            # Concatenate copilot results with eligible transcript frame
            transcript_frame_with_copilot = run_frame_concatenator(
                params=ParamsFrameConcatenator(
                    orient=OrientEnum.horizontal.value,
                ),
                source_frame=copilot_eligible_transcripts,
                transcript_copilot_frame=transcript_copilot_result,
            )

            transcript_final_frame = run_frame_concatenator(
                params=ParamsFrameConcatenator(
                    orient=OrientEnum.vertical.value, reset_index=True, drop_index=True
                ),
                source_frame=transcript_frame_with_copilot,
                skipped_copilot_df=skipped_copilot_df,
            )
        else:
            logger.info("No transcripts eligible for copilot processing")

        # Merge transcript fields into call frame
        call_final_frame = run_copilot_call_frame_with_transcript_fields(
            call_frame=call_source_frame,
            transcript_frame_with_copilot=transcript_final_frame,
            cloud_provider_prefix=cloud_provider_prefix,
        )

        # Handle potential duplicate columns
        call_final_frame = deduplicate_columns(df=call_final_frame)

        # Remove copilot-specific columns from transcript frame
        transcript_final_frame = run_filter_columns(
            source_frame=transcript_final_frame,
            params=ParamsFilterColumns(
                action=ActionEnum.drop,
                columns=[
                    CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS,
                    CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA,
                    TranscriptionTempColumns.SKIP_COPILOT,
                ]
                + SemanticModelFields.list(),
            ),
        )
    elif not call_source_frame.empty:
        logger.warning(
            "All calls were filtered out due to duration threshold. Skipping copilot processing."
        )
    else:
        logger.info("Processing frames without copilot (empty source data)")

    # Create the path where the Transcript ndjson result is to be uploaded
    transcript_ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        aries_task_input=aries_task_input,
        model=MetaModel.TRANSCRIPT,
    )

    # Write the transcript_final_frame into a ndjson file at the generated ndjson path
    run_write_ndjson(
        source_serializer_result=transcript_final_frame,
        output_filepath=transcript_ndjson_path,
        audit_output=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )
    transcript_output = add_nested_params(
        file_uri=transcript_ndjson_path,
        es_action=EsActionEnum.INDEX,
        data_model="se_elastic_schema.models.tenant.communication.transcript:Transcript",
    )

    # Create the path where the Call ndjson result is to be uploaded
    call_ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        aries_task_input=aries_task_input,
        model=MetaModel.CALL,
    )

    # Write the call_final_frame into a ndjson file at the generated ndjson path
    run_write_ndjson(
        source_serializer_result=call_final_frame,
        output_filepath=call_ndjson_path,
        audit_output=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )
    call_output = add_nested_params(
        file_uri=call_ndjson_path,
        es_action=EsActionEnum.UPDATE,
        data_model="se_elastic_schema.models.tenant.communication.call:Call",
    )

    # finish the flow with both transcript and call results
    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.TRANSCRIPT: transcript_output,
            MetaModel.CALL: call_output,
        },
    )
