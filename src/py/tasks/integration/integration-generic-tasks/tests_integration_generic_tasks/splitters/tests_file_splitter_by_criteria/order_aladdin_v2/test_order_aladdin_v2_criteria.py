import addict
import filecmp
import fsspec
import json
import os
import pytest
from se_io_utils.json_utils import read_json, write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"

import shutil
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from freezegun import freeze_time
from integration_generic_tasks.splitters.file_splitter_by_criteria.file_splitter_by_criteria_task import (  # noqa E501
    file_splitter_by_criteria_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.aws_helpers.s3_test_helpers import create_and_add_objects_to_s3_bucket
from mock.mock import patch
from moto import mock_aws
from pathlib import Path

BUCKET_NAME: str = "test.dev.steeleye.co"
TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

CURRENT_PATH = Path(__file__).parent
LOCAL_BUCKET_PATH = CURRENT_PATH.joinpath("data/buckets", BUCKET_NAME)

EXPECTED_RESULTS_PATH = CURRENT_PATH.joinpath("data/expected_results")
CLIENT_SIDE_BATCH = EXPECTED_RESULTS_PATH.joinpath(
    "AladdinTCA.Diogo_EQ.20241023_client_side_orders_batch_0.csv"
)
MARKET_SIDE_BATCH_1 = EXPECTED_RESULTS_PATH.joinpath(
    "AladdinTCA.Diogo_EQ.20241023_market_side_orders_batch_0.csv"
)
MARKET_SIDE_BATCH_2 = EXPECTED_RESULTS_PATH.joinpath(
    "AladdinTCA.Diogo_EQ.20241023_market_side_orders_batch_1.csv"
)
OTC_CLIENT_SIDE_BATCH = EXPECTED_RESULTS_PATH.joinpath(
    "AladdinTCA.OTC_DERIV.20241023_client_side_orders_batch_0.csv"
)
OTC_MARKET_SIDE_BATCH_1 = EXPECTED_RESULTS_PATH.joinpath(
    "AladdinTCA.OTC_DERIV.20241023_market_side_orders_batch_0.csv"
)
CACHE = EXPECTED_RESULTS_PATH.joinpath(
    "68bb50d933a57a1330d3cb193a148345a77bcda46fcd3749341ee16f4cad4e5f___order_id_cache.json"
)
OTC_CACHE = EXPECTED_RESULTS_PATH.joinpath(
    "12aa462fe57711c924bfdaf7167e50125c21e264ab04ae1487cbb95dfdf1c3d6___order_id_cache.json"
)
BROKER_MY_MARKET_FIRM = EXPECTED_RESULTS_PATH.joinpath("AladdinTCA.Diogo_EQ.Broker.20241023.csv")
PORTFOLIO_MY_MARKET_FIRM = EXPECTED_RESULTS_PATH.joinpath(
    "AladdinTCA.Diogo_EQ.Portfolio.20241023.csv"
)
SCHRODERS_PORTFOLIO_MY_MARKET_FIRM = EXPECTED_RESULTS_PATH.joinpath(
    "AladdinTCA.Schroders_EQ.Portfolio.20241023.csv"
)
MOCK_TENANT_WORKFLOW_CONFIG = addict.Dict(
    {
        "tenant": {
            "cloud": "aws",
            "lake_prefix": f"s3://{BUCKET_NAME}/",
            "realm": "test.dev.steeleye.co",
        },
        "workflow": {
            "streamed": False,
            "name": OrderWorkflowNames.ORDER_ALADDIN_V2,
            "tenant": "test",
        },
        "max_batch_size": 50,
    },
)

mock_aiobotocore_convert_to_response_dict()


@mock_aws
@freeze_time(time_to_freeze="2024-01-22 07:00:00.000000+00:00")
class TestOrderAladdinV2Criteria:
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=MOCK_TENANT_WORKFLOW_CONFIG,
    )
    def test_execution_of_skippable_file(self, mocker, sample_input_skippable_file, caplog):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_input_skippable_file
        )

        assert aries_task_result.output_param.params == {
            "client_side_orders": {"dynamicTaskInputs": {}, "dynamicTasks": []},
            "market_side_orders": {"dynamicTaskInputs": {}, "dynamicTasks": []},
        }

        assert (
            "The input file: AladdinTCA.Diogo_EQ.Employee.20241023.csv does not have to be processed."  # noqa: E501
            in caplog.text
        )

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=MOCK_TENANT_WORKFLOW_CONFIG,
    )
    def test_normal_execution(
        self,
        mocker,
        sample_input_file,
    ):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        aries_task_result = file_splitter_by_criteria_run(aries_task_input=sample_input_file)

        fs, _, (_,) = fsspec.get_fs_token_paths(f"s3://{BUCKET_NAME}")
        results_path = f"s3://{BUCKET_NAME}/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria"
        result_files = fs.ls(results_path)
        final_result_files = []

        dir = f"{LOCAL_BUCKET_PATH}/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria"  # noqa: E501
        os.makedirs(dir, exist_ok=True)

        for file in result_files:
            with fs.open(f"s3://{file}", "rb") as mock_f:
                content = mock_f.read()

            local_path = f"{LOCAL_BUCKET_PATH.as_posix().replace(BUCKET_NAME, '')}{file}"
            with open(local_path, "w") as f:
                f.write(content.decode())

            final_result_files.append(local_path)

        assert filecmp.cmp(CACHE, final_result_files[0], shallow=False)
        assert filecmp.cmp(CLIENT_SIDE_BATCH, final_result_files[1], shallow=False)
        assert filecmp.cmp(MARKET_SIDE_BATCH_1, final_result_files[2], shallow=False)
        assert filecmp.cmp(MARKET_SIDE_BATCH_2, final_result_files[3], shallow=False)

        shutil.rmtree(dir)

        assert aries_task_result.output_param.params == {
            "client_side_orders": {
                "dynamicTaskInputs": {
                    "client_side_orders_processing_0": {
                        "io_param": {
                            "params": {
                                "file_uri": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria/AladdinTCA.Diogo_EQ.20241023_client_side_orders_batch_0.csv",
                                "flow_override": "client",
                                "order_id_cache_path": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria/68bb50d933a57a1330d3cb193a148345a77bcda46fcd3749341ee16f4cad4e5f___order_id_cache.json",
                                "source_file_uri": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Diogo_EQ.Transaction.20241023.csv",
                            }
                        },
                        "workflow": {
                            "name": "order_aladdin_v2",
                            "stack": "dev-shared-2",
                            "start_timestamp": "2024-01-22T00:00:00",
                            "tenant": "test",
                            "trace_id": "foo",
                        },
                    }
                },
                "dynamicTasks": [
                    {
                        "subWorkflowParam": {"name": "order_aladdin_v2_subworkflow"},
                        "taskReferenceName": "client_side_orders_processing_0",
                        "type": "SUB_WORKFLOW",
                    }
                ],
            },
            "market_side_orders": {
                "dynamicTaskInputs": {
                    "market_side_orders_processing_0": {
                        "io_param": {
                            "params": {
                                "file_uri": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria/AladdinTCA.Diogo_EQ.20241023_market_side_orders_batch_0.csv",
                                "flow_override": "market",
                                "order_id_cache_path": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria/68bb50d933a57a1330d3cb193a148345a77bcda46fcd3749341ee16f4cad4e5f___order_id_cache.json",
                                "source_file_uri": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Diogo_EQ.Transaction.20241023.csv",
                            }
                        },
                        "workflow": {
                            "name": "order_aladdin_v2",
                            "stack": "dev-shared-2",
                            "start_timestamp": "2024-01-22T00:00:00",
                            "tenant": "test",
                            "trace_id": "foo",
                        },
                    },
                    "market_side_orders_processing_1": {
                        "io_param": {
                            "params": {
                                "file_uri": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria/AladdinTCA.Diogo_EQ.20241023_market_side_orders_batch_1.csv",
                                "flow_override": "market",
                                "order_id_cache_path": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/foo/file_splitter_by_criteria/68bb50d933a57a1330d3cb193a148345a77bcda46fcd3749341ee16f4cad4e5f___order_id_cache.json",
                                "source_file_uri": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Diogo_EQ.Transaction.20241023.csv",
                            }
                        },
                        "workflow": {
                            "name": "order_aladdin_v2",
                            "stack": "dev-shared-2",
                            "start_timestamp": "2024-01-22T00:00:00",
                            "tenant": "test",
                            "trace_id": "foo",
                        },
                    },
                },
                "dynamicTasks": [
                    {
                        "subWorkflowParam": {"name": "order_aladdin_v2_subworkflow"},
                        "taskReferenceName": "market_side_orders_processing_0",
                        "type": "SUB_WORKFLOW",
                    },
                    {
                        "subWorkflowParam": {"name": "order_aladdin_v2_subworkflow"},
                        "taskReferenceName": "market_side_orders_processing_1",
                        "type": "SUB_WORKFLOW",
                    },
                ],
            },
        }

    def test_otc_trades(
        self,
        mocker,
        sample_input_file_otc_trades,
    ):
        """
        The input OrderDetail file consists of 7 orders as follows:
        - 2 OTC Orders
        - 1 regular non-OTC Order
        - 1 Order without `origOrderId` and `orderId`, which will be discarded
        - 1 Order without `origOrder` and with order.orderStatus != "F", which will be discarded
        - 1 Order without `origOrder` and with order.ownerType == "P", which will be discarded
        - 1 Order without `origOrder` and with transaction.secType not containing "OTC",
            which will be discarded

        The resulting client_side batch is expected to have 3 orders.
        The result market_side batch is expected to have 1 order. The reason for this
        is that OTC trades do not have an associated Placement, hence no Fills as well.
        """
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=MOCK_TENANT_WORKFLOW_CONFIG,
        )

        mock_task = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        mock_task.side_effect = write_named_temporary_json_side_effect
        mocker.patch("integration_audit.auditor.write_json")

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_input_file_otc_trades
        )

        fs, _, (_,) = fsspec.get_fs_token_paths(f"s3://{BUCKET_NAME}")
        results_path = f"s3://{BUCKET_NAME}/aries/ingest/order_aladdin_v2/2024/01/22/test_otc_trades/file_splitter_by_criteria"
        result_files = fs.ls(results_path)
        final_result_files = []

        dir = f"{LOCAL_BUCKET_PATH}/aries/ingest/order_aladdin_v2/2024/01/22/test_otc_trades/file_splitter_by_criteria"  # noqa: E501
        os.makedirs(dir, exist_ok=True)

        for file in result_files:
            with fs.open(f"s3://{file}", "rb") as mock_f:
                content = mock_f.read()

            local_path = f"{LOCAL_BUCKET_PATH.as_posix().replace(BUCKET_NAME, '')}{file}"
            with open(local_path, "w") as f:
                f.write(content.decode())

            final_result_files.append(local_path)

        assert filecmp.cmp(OTC_CACHE, final_result_files[0], shallow=False)
        assert filecmp.cmp(OTC_CLIENT_SIDE_BATCH, final_result_files[1], shallow=False)
        assert filecmp.cmp(OTC_MARKET_SIDE_BATCH_1, final_result_files[2], shallow=False)

        shutil.rmtree(dir)

        assert aries_task_result.output_param.params == {
            "client_side_orders": {
                "dynamicTaskInputs": {
                    "client_side_orders_processing_0": {
                        "io_param": {
                            "params": {
                                "file_uri": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/test_otc_trades/file_splitter_by_criteria/AladdinTCA.OTC_DERIV.20241023_client_side_orders_batch_0.csv",
                                "flow_override": "client",
                                "order_id_cache_path": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/test_otc_trades/file_splitter_by_criteria/12aa462fe57711c924bfdaf7167e50125c21e264ab04ae1487cbb95dfdf1c3d6___order_id_cache.json",
                                "source_file_uri": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.OTC_DERIV.Transaction.20241023.csv",
                            }
                        },
                        "workflow": {
                            "name": "order_aladdin_v2",
                            "stack": "dev-shared-2",
                            "start_timestamp": "2024-01-22T00:00:00",
                            "tenant": "test",
                            "trace_id": "test_otc_trades",
                        },
                    }
                },
                "dynamicTasks": [
                    {
                        "subWorkflowParam": {"name": "order_aladdin_v2_subworkflow"},
                        "taskReferenceName": "client_side_orders_processing_0",
                        "type": "SUB_WORKFLOW",
                    }
                ],
            },
            "market_side_orders": {
                "dynamicTaskInputs": {
                    "market_side_orders_processing_0": {
                        "io_param": {
                            "params": {
                                "file_uri": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/test_otc_trades/file_splitter_by_criteria/AladdinTCA.OTC_DERIV.20241023_market_side_orders_batch_0.csv",
                                "flow_override": "market",
                                "order_id_cache_path": "s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/2024/01/22/test_otc_trades/file_splitter_by_criteria/12aa462fe57711c924bfdaf7167e50125c21e264ab04ae1487cbb95dfdf1c3d6___order_id_cache.json",
                                "source_file_uri": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.OTC_DERIV.Transaction.20241023.csv",
                            }
                        },
                        "workflow": {
                            "name": "order_aladdin_v2",
                            "stack": "dev-shared-2",
                            "start_timestamp": "2024-01-22T00:00:00",
                            "tenant": "test",
                            "trace_id": "test_otc_trades",
                        },
                    }
                },
                "dynamicTasks": [
                    {
                        "subWorkflowParam": {"name": "order_aladdin_v2_subworkflow"},
                        "taskReferenceName": "market_side_orders_processing_0",
                        "type": "SUB_WORKFLOW",
                    }
                ],
            },
        }

        audit_result = read_json(AUDIT_PATH.as_posix())
        assert audit_result == {
            "input_records": {
                "OrderDetail|3": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": [
                            "orderDetail.origOrderId is NULL and order.orderStatus is not ´F´",
                        ],
                        "updated": 0,
                    }
                },
                "OrderDetail|4": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": [
                            "orderDetail.origOrderId is NULL and order.orderStatus is not ´F´",
                        ],
                        "updated": 0,
                    }
                },
                "OrderDetail|5": {
                    "Order": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": [
                            "orderDetail.origOrderId is NULL and order.ownerType is not NULL",
                        ],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": [],
        }

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=MOCK_TENANT_WORKFLOW_CONFIG,
    )
    @pytest.mark.parametrize(
        "file_uri, market_type, expected_result_file_path",
        [
            (
                "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Diogo_EQ.Broker.20241023.csv",
                "Broker",
                BROKER_MY_MARKET_FIRM,
            ),
            (
                "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Diogo_EQ.Portfolio.20241023.csv",
                "Portfolio",
                PORTFOLIO_MY_MARKET_FIRM,
            ),
        ],
    )
    def test_execution_of_my_market_files(
        self, mocker, file_uri, market_type, expected_result_file_path, sample_input_skippable_file
    ):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        sample_input_market = sample_input_skippable_file
        sample_input_market.input_param.params["file_uri"] = file_uri
        aries_task_result = file_splitter_by_criteria_run(aries_task_input=sample_input_market)

        assert aries_task_result.output_param.params == {
            "client_side_orders": {"dynamicTaskInputs": {}, "dynamicTasks": []},
            "market_side_orders": {"dynamicTaskInputs": {}, "dynamicTasks": []},
        }

        target_file_path = f"s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-firm/order_aladdin_v2/{market_type.lower()}/AladdinTCA.Diogo_EQ.{market_type}.20241023.csv"

        fs, _, (_,) = fsspec.get_fs_token_paths(f"s3://{BUCKET_NAME}")

        local_result_file = (
            f"{LOCAL_BUCKET_PATH}/flows/mymarket-universal-steeleye-firm/"
            f"order_aladdin_v2/{market_type.lower()}"
            f"/AladdinTCA.Diogo_EQ.{market_type}.20241023.csv"
        )  # noqa: E501
        os.makedirs("/".join(local_result_file.split("/")[:-1]), exist_ok=True)

        with fs.open(target_file_path, "rb") as mock_f:
            content = mock_f.read()

        with open(local_result_file, "w") as f:
            f.write(content.decode())

        assert filecmp.cmp(expected_result_file_path, local_result_file, shallow=False)

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=MOCK_TENANT_WORKFLOW_CONFIG,
    )
    @pytest.mark.parametrize(
        "file_uri, market_type, expected_result_file_path",
        [
            (
                "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.Schroders_EQ.Portfolio.20241023.csv",
                "Portfolio",
                SCHRODERS_PORTFOLIO_MY_MARKET_FIRM,
            ),
        ],
    )
    def test_schroders_portfolio_my_market_files(
        self, mocker, file_uri, market_type, expected_result_file_path, sample_input_skippable_file
    ):
        # Test that the schroders tenant override is working as expected,
        # and that the output file swaps the Name and FirmDetailsIndustry compared
        # to the default scenario test.
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        sample_input_market = sample_input_skippable_file
        sample_input_market.workflow.tenant = "schroders"
        sample_input_market.input_param.params["file_uri"] = file_uri
        aries_task_result = file_splitter_by_criteria_run(aries_task_input=sample_input_market)

        assert aries_task_result.output_param.params == {
            "client_side_orders": {"dynamicTaskInputs": {}, "dynamicTasks": []},
            "market_side_orders": {"dynamicTaskInputs": {}, "dynamicTasks": []},
        }

        target_file_path = f"s3://test.dev.steeleye.co/flows/mymarket-universal-steeleye-firm/order_aladdin_v2/{market_type.lower()}/AladdinTCA.Schroders_EQ.{market_type}.20241023.csv"

        fs, _, (_,) = fsspec.get_fs_token_paths(f"s3://{BUCKET_NAME}")

        local_result_file = (
            f"{LOCAL_BUCKET_PATH}/flows/mymarket-universal-steeleye-firm/"
            f"order_aladdin_v2/{market_type.lower()}"
            f"/AladdinTCA.Schroders_EQ.{market_type}.20241023.csv"
        )  # noqa: E501
        os.makedirs("/".join(local_result_file.split("/")[:-1]), exist_ok=True)

        with fs.open(target_file_path, "rb") as mock_f:
            content = mock_f.read()

        with open(local_result_file, "w") as f:
            f.write(content.decode())

        assert filecmp.cmp(expected_result_file_path, local_result_file, shallow=False)


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)
