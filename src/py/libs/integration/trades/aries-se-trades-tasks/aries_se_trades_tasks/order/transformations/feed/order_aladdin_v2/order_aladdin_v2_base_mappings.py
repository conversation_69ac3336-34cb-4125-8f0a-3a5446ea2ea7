import pandas as pd
import re
from aries_se_core_tasks.currency.convert_minor_to_major import (  # type: ignore[attr-defined]
    run_convert_minor_to_major,
)
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined]
    run_convert_datetime,
)
from aries_se_core_tasks.transform.dataframe.concat_attributes import (  # type: ignore[attr-defined]
    run_concat_attributes,
)
from aries_se_core_tasks.transform.map.map_attribute import (  # type: ignore[attr-defined]
    run_map_attribute,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]
    run_map_conditional,
)
from aries_se_core_tasks.transform.map.map_value import run_map_value
from aries_se_core_tasks.utilities.static import Delimiters
from aries_se_trades_tasks.instrument.instrument_identifiers import run_instrument_identifiers
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    ALADDIN_FILES_PATTERN,
    DevColumns,
    FileTypeAssetClass,
    FileTypes,
    OrderDetailSourceColumns,
    OrderSourceColumns,
    PlacementSourceColumns,
    SecGroupValues,
    SecTypeValues,
    TransactionSourceColumns,
)
from aries_se_trades_tasks.orders_and_tr.identifiers.merge_market_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    run_merge_market_identifiers,
)
from pathlib import Path
from se_core_tasks.currency.convert_minor_to_major import Params as ParamsConvertMinorToMajor
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.datetime.convert_datetime import Params as ParamsConvertDatetime
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_attribute import Params as ParamsMapAttribute
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.map.map_value import Params as ParamsMapValue
from se_core_tasks.transform.concat_attributes import Params as ParamsConcatAttributes
from se_elastic_schema.models import Order
from se_elastic_schema.static.mifid2 import (
    BuySellIndicator,
    OptionType,
    PriceNotation,
    QuantityNotation,
)
from se_io_utils.json_utils import read_json
from se_trades_tasks.abstractions.abstract_order_transformations import AbstractOrderTransformations
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    run_generic_order_party_identifiers,
)
from se_trades_tasks.order.static import (
    ModelPrefix,
    OrderColumns,
    add_prefix,
)
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from se_trades_tasks.order_and_tr.instrument.identifiers.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from se_trades_tasks.order_and_tr.instrument.identifiers.symbol_and_expiry_code.utils import (
    underlying_symbol_and_expiry_code_replacements,
)
from se_trades_tasks.order_and_tr.static import AssetClass, PartyPrefix
from typing import Dict, List, Tuple


class OrderAladdinV2BaseMappings(AbstractOrderTransformations):  # type: ignore[misc]
    def __init__(
        self,
        source_file_uri: str,
        file_uri: str,
        tenant: str,
        es_client,
        order_id_cache_path: str,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.tenant = tenant
        self.source_file_uri: str = source_file_uri
        self.file_uri: str = file_uri
        self.file_type_asset_class: str = self._get_file_type_asset_class(file_uri=self.file_uri)
        self.es_client = es_client
        self.order_id_cache_dict = read_json(path=order_id_cache_path)

        self.pre_process_df: pd.DataFrame
        self.source_frame: pd.DataFrame
        self.target_df: pd.DataFrame
        self.cancelled_quantity_diff_by_order_id_map: Dict[str, float] = {}
        self.is_otc_mask: pd.Series = self._build_is_otc_mask()

    def _pre_process(self):
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._get_buy_sell(),
                self._get_currency(),
                self._get_notional_currency_2(),
                self._get_asset_class(),
                self._get_instrument_classification(),
                self._get_expiry_date(),
                self._get_option_strike_price(),
            ],
            axis=1,
        )
        # _get_option_type() depends on _get_asset_class()
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._get_option_type(),
            ],
            axis=1,
        )

        self.pre_process_df.loc[:, DevColumns.FILE_TYPE_ASSET_CLASS] = self.file_type_asset_class

    def process(self) -> pd.DataFrame:
        self.pre_process()

        # SteelEye utility mappings
        self.meta_model()
        self.data_source_name()
        self.source_key()
        self.source_index()
        self.is_synthetic()

        # Timestamp mappings
        self.date()
        self.timestamps_order_received()
        self.timestamps_internal_order_received()
        self.timestamps_order_submitted()
        self.timestamps_internal_order_submitted()
        self.timestamps_order_status_updated()
        self.transaction_details_trading_date_time()
        self.timestamps_trading_date_time()
        self.timestamps_validity_period()

        # Identifier mappings
        self.id()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_parent_order_id()
        self.order_identifiers_order_id_code()
        self.report_details_transaction_ref_no()
        self.order_identifiers_transaction_ref_no()
        self.transaction_details_basket_id()
        self.order_identifiers_initial_order_designation()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.transaction_details_complex_trade_component_id()
        self.order_identifiers_sequence_number()
        self.order_identifiers_order_routing_code()

        # Order details mappings
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_trading_capacity()
        self.transaction_details_trading_capacity()
        self.buy_sell()
        self.execution_details_buy_sell_indicator()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_record_type()
        self.order_class()

        # Prices mappings
        self.execution_details_limit_price()
        self.execution_details_stop_price()
        self.price_forming_data_price()
        self.transaction_details_price()
        self.transaction_details_price_notation()
        self.transaction_details_price_currency()

        # Quantity mappings
        self.price_forming_data_initial_quantity()
        self.execution_details_order_status()  # depends on priceFormingData.initialQuantity
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.price_forming_data_traded_quantity()  # depends on executionDetails.orderStatus
        self.transaction_details_quantity()

        # Trade details mappings
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()

        # Instrument and Party mappings
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()

        self.post_process()

        return self.target_df

    def _buy_sell(self) -> pd.DataFrame:
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, DevColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL)],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, DevColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL)],
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="Aladdin",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        result: pd.DataFrame = run_map_value(
            source_frame=self.pre_process_df,
            params=ParamsMapValue(
                source_attribute=DevColumns.BUY_SELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            skip_serializer=True,
        )
        return result

    def _execution_details_limit_price(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.LIMIT_VALUE)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE],
        )

    def _execution_details_order_status(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=pd.NA,  # type: ignore[call-overload]
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_STATUS],
        )
        return result

    def _execution_details_order_type(self) -> pd.DataFrame:
        value_map = {
            "M": "Market",
            "L": "Limit",
            "S": "Stop",
            "X": "Stop Limit",
            "5": "Market on Close",
            "O": "Market on Open",
            "B": "Limit on Close",
            "V": "Market on Close",
            "C": "Market on Cash Close",
            "I": "Funari",
            "W": "WMR Close",
            "P": "EFP",
        }

        result: pd.DataFrame = run_map_value(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_TYPE),
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                case_insensitive=True,
                value_map=value_map,
                default_value="Market",
            ),
            skip_serializer=True,
        )
        return result

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """
        order.[basketId]
        'assetId: ' + transaction.[assetId]
        order.[genComments]
        transaction.[execCptyType]
        """

        asset_id = run_map_attribute(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.ASSET_ID
                ),
                target_attribute=DevColumns.ASSET_ID,
                prefix=f"{TransactionSourceColumns.ASSET_ID}: ",
            ),
            skip_serializer=True,
        )

        result: pd.DataFrame = run_concat_attributes(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[
                        :,
                        [
                            add_prefix(FileTypes.ORDER, OrderSourceColumns.BASKET_ID),
                            add_prefix(FileTypes.ORDER, OrderSourceColumns.GEN_COMMENTS),
                            add_prefix(
                                FileTypes.TRANSACTION, TransactionSourceColumns.EXEC_CPCY_TYPE
                            ),
                        ],
                    ],
                    asset_id,
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.BASKET_ID),
                    DevColumns.ASSET_ID,
                    add_prefix(FileTypes.ORDER, OrderSourceColumns.GEN_COMMENTS),
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.EXEC_CPCY_TYPE),
                ],
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                delimiter=Delimiters.COMMA_SPACE,
            ),
            skip_serializer=True,
        )
        return result

    def _execution_details_stop_price(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.PLACEMENT, PlacementSourceColumns.STOP_VALUE)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_STOP_PRICE],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        value_map = {
            "2": "GTCV",
            "1": "DAVY",
        }

        timestamps_validity_period_df: pd.DataFrame = run_map_value(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=add_prefix(FileTypes.ORDER, OrderSourceColumns.TIME_IN_FORCE),
                target_attribute=DevColumns.TIMESTAMPS_VALIDITY_PERIOD,
                case_insensitive=True,
                value_map=value_map,
            ),
            skip_serializer=True,
        )

        result: pd.DataFrame = run_map_attribute(
            source_frame=timestamps_validity_period_df,
            params=ParamsMapAttribute(
                source_attribute=DevColumns.TIMESTAMPS_VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to=CastTo.STRING_LIST,
                list_delimiter=",",
            ),
            skip_serializer=True,
        )
        return result

    def _id(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=pd.NA,  # type: ignore[call-overload]
            index=self.source_frame.index,
            columns=[OrderColumns.ID],
        )
        return result

    def _is_synthetic(self):
        """All NEWOs generated in the Aladdin feed are synthetic."""
        return pd.DataFrame(
            data=[True] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.IS_SYNTHETIC)],
        )

    def _market_identifiers(self) -> pd.DataFrame:
        result: pd.DataFrame = run_merge_market_identifiers(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
            skip_serializer=True,
        )
        return result

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        source_frame_cols = [
            add_prefix(FileTypes.ORDER, OrderSourceColumns.PRICE_CCY),
            add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.MATURITY),
        ]
        source_frame = pd.concat(
            [
                self.pre_process_df,
                self.source_frame.loc[:, source_frame_cols],
                self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
            ],
            axis=1,
        )

        source_frame.loc[:, DevColumns.ISIN] = self.source_frame.loc[
            :, add_prefix(FileTypes.ORDER, OrderSourceColumns.ISIN)
        ].fillna(
            self.source_frame.loc[:, add_prefix(FileTypes.TRANSACTION, OrderSourceColumns.ISIN)]
        )

        loans_mask = (
            self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
            ]
            .astype("string")
            .str.fullmatch(SecGroupValues.LOAN, case=False, na=False)
        )

        source_frame.loc[loans_mask, DevColumns.ISIN] = pd.NA

        symbol_mapping_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER),
                ],
                self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
                self.pre_process_df.loc[:, DevColumns.ASSET_CLASS],
            ],
            axis=1,
        )
        underlying_symbol_and_expiry_code_df = self._get_underlying_symbol_and_expiry_code(
            df=symbol_mapping_df
        )

        result: pd.DataFrame = run_instrument_identifiers(
            source_frame=pd.concat([source_frame, underlying_symbol_and_expiry_code_df], axis=1),
            params=ParamsInstrumentIdentifiers(
                currency_attribute=DevColumns.CURRENCY,
                notional_currency_2_attribute=DevColumns.NOTIONAL_CURRENCY_2,
                expiry_date_attribute=DevColumns.EXPIRY_DATE,
                isin_attribute=DevColumns.ISIN,
                option_strike_price_attribute=DevColumns.STRIKE_PRICE,
                underlying_symbol_attribute=DevColumns.SYMBOL,
                underlying_symbol_expiry_code_attribute=DevColumns.SYMBOL_AND_EXPIRY_CODE,
                asset_class_attribute=DevColumns.ASSET_CLASS,
                option_type_attribute=DevColumns.OPTION_TYPE,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                instrument_classification_attribute=DevColumns.INSTRUMENT_CLASSIFICATION,
                retain_task_inputs=True,
            ),
            skip_serializer=True,
        )
        return result

    def _market_identifiers_parties(self) -> pd.DataFrame:
        parties_source_frame = pd.concat(
            [
                self.source_frame.loc[:, DevColumns.EXECUTING_ENTITY_WITH_LEI],
                self._get_trader(),
                self._get_client(),
                self._get_investment_decision_within_firm(),
                self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
            ],
            axis=1,
        )

        result: pd.DataFrame = run_generic_order_party_identifiers(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                counterparty_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                client_identifier=DevColumns.CLIENT,
                executing_entity_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                buyer_identifier=DevColumns.CLIENT,
                seller_identifier=DevColumns.EXECUTING_ENTITY_WITH_LEI,
                trader_identifier=DevColumns.TRADER,
                investment_decision_within_firm_identifier=DevColumns.INVESTMENT_DECISION_WITHIN_FIRM,
                execution_within_firm_identifier=DevColumns.TRADER,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                create_fallback_fields=True,
            ),
            skip_serializer=True,
        )
        return result

    def _meta_model(self) -> pd.DataFrame:
        """__meta_model__ is required downstream for the AssignMetaParent
        task."""

        order_model_name = Order.get_reference().name

        return pd.concat(
            [
                pd.DataFrame(
                    data=order_model_name,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.META_MODEL)],
                ),
                pd.DataFrame(
                    data=order_model_name,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.META_MODEL)],
                ),
            ],
            axis=1,
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION],
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID],
        )

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.target_df.loc[
                        :,
                        add_prefix(
                            ModelPrefix.ORDER, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        ),
                    ].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER,
                            OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.target_df.loc[
                        :,
                        add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        ),
                    ].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _report_details_transaction_ref_no(self):
        return pd.DataFrame(
            data=pd.NA,  # type: ignore[call-overload]
            index=self.source_frame.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_key(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.file_uri,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=pd.NA,  # type: ignore[call-overload]
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )
        return result

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.PORTFOLIO_TICKER)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BASKET_ID],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_NUM)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE],
        )

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )
        return result

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID],
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=self.pre_process_df.loc[:, DevColumns.CURRENCY].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )
        return result

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        price_notation_map = {
            "DERIV": PriceNotation.MONE.value,
            "FX": PriceNotation.MONE.value,
            "FI": PriceNotation.PERC.value,
            "EQ": PriceNotation.MONE.value,
        }
        price_notation = price_notation_map.get(self.file_type_asset_class, pd.NA)

        result: pd.DataFrame = pd.DataFrame(
            data=price_notation,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )
        return result

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=self.pre_process_df.loc[:, DevColumns.CURRENCY].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )
        return result

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        sec_type = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)

        result: pd.DataFrame = run_map_conditional(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, sec_type],
                    self.pre_process_df.loc[:, DevColumns.FILE_TYPE_ASSET_CLASS],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query=f"(`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('DERIV', case=False, na=False)) &"  # noqa: E501
                        f" ~(`{sec_type}`.str.fullmatch('CSWAP', case=False, na=False))",
                        value=QuantityNotation.UNIT.value,
                    ),
                    Case(
                        query=f"(`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('DERIV', case=False, na=False)) &"  # noqa: E501
                        f" (`{sec_type}`.str.fullmatch('CSWAP', case=False, na=False))",
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FX', case=False, na=False)",  # noqa: E501
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FI', case=False, na=False)",  # noqa: E501
                        value=QuantityNotation.NOML.value,
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('EQ', case=False, na=False)",  # noqa: E501
                        value=QuantityNotation.UNIT.value,
                    ),
                ],
            ),
            skip_serializer=True,
        )
        return result

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=pd.NA,  # type: ignore[call-overload]
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )
        return result

    def _post_process(self):
        # Initialise cols to NA so that they are always populated.
        for col in {
            DevColumns.RIC,
            DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
        }:
            self.target_df.loc[:, col] = pd.NA

        # Propagate Asset Class for InstrumentFallback
        self.target_df.loc[:, DevColumns.ASSET_CLASS] = self.pre_process_df.loc[
            :, DevColumns.ASSET_CLASS
        ]

        # Populate Instrument Unique Identifier and RIC for EQ and DERIV asset classes.
        if self.file_type_asset_class in (
            FileTypeAssetClass.EQ,
            FileTypeAssetClass.DERIV,
        ):
            self.target_df.loc[:, DevColumns.RIC] = self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
            ]

            ric_prefix = "aladdin-ric-"
            self.target_df.loc[:, DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER] = (
                ric_prefix
                + self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.RIC)
                ].astype("string")
            )

        # Fallback for Instrument Unique Identifier: populate first value from marketIdentifiers.
        iue_from_market_ids_mask = (
            self.target_df[OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT].notnull()
            & self.target_df[DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER].isnull()
        )
        self.target_df.loc[iue_from_market_ids_mask, DevColumns.INSTRUMENT_UNIQUE_IDENTIFIER] = (
            self.target_df.loc[iue_from_market_ids_mask, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT]
            .str.get(0)
            .str.get("labelId")
        )

        # No input records are NEWOs. This information is passed downstream
        # to the RemoveDuplicateNEWOS Task.
        self.target_df.loc[:, DevColumns.NEWO_IN_FILE] = self._get_newo_in_file()

        # Prepare InstrumentFullName override and instrument fallback
        self.target_df.loc[:, DevColumns.INSTRUMENT_FULL_NAME] = self.source_frame.loc[
            :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1)
        ]
        self._instrument_name_fallback_from_cache()

        # Propagate Instrument Classification to instrument fallback
        self.target_df.loc[:, DevColumns.INSTRUMENT_CLASSIFICATION] = self.pre_process_df.loc[
            :, DevColumns.INSTRUMENT_CLASSIFICATION
        ]

        self.target_df.loc[:, DevColumns.FILE_TYPE_ASSET_CLASS] = self.pre_process_df.loc[
            :, DevColumns.FILE_TYPE_ASSET_CLASS
        ]

        self.target_df.loc[:, DevColumns.INSTRUMENT_CREATED_THROUGH_FB] = True

        self.target_df.loc[:, DevColumns.BEST_EX_ASSET_CLASS_MAIN] = run_map_conditional(
            source_frame=self.target_df,
            params=MapConditionalParams(
                target_attribute=DevColumns.BEST_EX_ASSET_CLASS_MAIN,
                cases=[
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('DERIV', case=False, na=False)",  # noqa: E501
                        value="Derivatives",
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FX', case=False, na=False)",  # noqa: E501
                        value="Currency Derivatives",
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('FI', case=False, na=False)",  # noqa: E501
                        value="Debt Instruments",
                    ),
                    Case(
                        query=f"`{DevColumns.FILE_TYPE_ASSET_CLASS}`.str.fullmatch('EQ', case=False, na=False)",  # noqa: E501
                        value="Equity",
                    ),
                ],
            ),
            skip_serializer=True,
        )

        self.target_df.loc[:, DevColumns.STRIKE_PRICE_CURRENCY] = self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ]
        # strike_price_currency must only be populated for options
        is_option_mask = self.pre_process_df.loc[:, DevColumns.ASSET_CLASS] == AssetClass.OPTION
        self.target_df.loc[~is_option_mask, DevColumns.STRIKE_PRICE_CURRENCY] = pd.NA

    def _instrument_name_fallback_from_cache(self):
        """See https://steeleye.atlassian.net/browse/ON-4615 for more details.

        For some Portfolio IDs we may not have a linking transaction, hence we would
        not have access to the traded instrument's name. Thus, upstream in the workflow
        we create a cache with the instrument name for each order ID.

        With this method, even if we receive a specific order for a specific Portfolio ID
        with missing data points, we try to recover it from the cache i.e. from another
        Portfolio ID (for the same Order ID) that has the same traded instrument.
        """

        null_instrument_name_mask = self.target_df.loc[:, DevColumns.INSTRUMENT_FULL_NAME].isnull()

        # handles edge case where the order id cache is empty
        if null_instrument_name_mask.any() and self.order_id_cache_dict:
            # handles scenario where the order_id_cache does not have
            # a matching order ID, or the instrument_full_name is missing
            # (result will remain the same = pd.NA or np.nan)
            self.target_df.loc[null_instrument_name_mask, DevColumns.INSTRUMENT_FULL_NAME] = (
                self.source_frame.loc[
                    null_instrument_name_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                ]
                .astype("string")
                .map(self.order_id_cache_dict)
                .str.get("instrument_full_name")
            )

    def _get_buy_sell(self) -> pd.Series:
        # Market and Client child classes extend this behavior
        value_map = {
            "BUY": "1",
            "BUYCLOSE": "1",
            "BUYPROT INIT": "2",
            "BUYPROT UNWIND": "2",
            "PAYBASE UNWIND": "2",
            "PAYFIX INIT": "2",
            "PAYFIX UNWIND": "2",
            "PAYPFL INIT": "2",
            "RCVBASE INIT": "1",
            "RCVFIX INIT": "1",
            "RCVFIX UNWIND": "1",
            "RECPFL INIT": "1",
            "SELL": "2",
            "SELLOPEN": "2",
            "SELLPROT INIT": "1",
            "SELLPROT UNWIND": "1",
        }

        result: pd.DataFrame = run_map_value(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=add_prefix(FileTypes.ORDER, OrderSourceColumns.TRAN_TYPE),
                target_attribute=DevColumns.BUY_SELL,
                case_insensitive=True,
                value_map=value_map,
            ),
            skip_serializer=True,
        )
        return result[DevColumns.BUY_SELL]

    def _get_currency(self) -> pd.Series:
        """
        ConvertMinorToMajor is being used to map the CNH -> CNY currencies
        NOTE: The prices are not being converted at the minor-major rate.

        order.[priceCcy]
        FOR FX Transaction.fxBaseCcy
        """
        if self.file_type_asset_class == FileTypeAssetClass.FX:
            result: pd.DataFrame = run_convert_minor_to_major(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.FX_BASE_CCY
                    ),
                    target_ccy_attribute=DevColumns.CURRENCY,
                ),
                skip_serializer=True,
            )
            return result[DevColumns.CURRENCY]

        result: pd.DataFrame = run_convert_minor_to_major(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=add_prefix(FileTypes.ORDER, OrderSourceColumns.PRICE_CCY),
                target_ccy_attribute=DevColumns.CURRENCY,
            ),
            skip_serializer=True,
        )
        return result[DevColumns.CURRENCY]

    def _get_asset_class(self) -> pd.Series:
        value_map = {
            SecGroupValues.ABS: AssetClass.BOND,
            SecGroupValues.BND: AssetClass.BOND,
            SecGroupValues.CASH: AssetClass.BOND,
            SecGroupValues.CMBS: AssetClass.BOND,
            SecGroupValues.CMO: AssetClass.BOND,
            SecGroupValues.EQUITY: AssetClass.EQUITY,
            SecGroupValues.FUTURE: AssetClass.FUTURE,
            SecGroupValues.LOAN: "loan",
            SecGroupValues.MBS: AssetClass.BOND,
            SecGroupValues.OPTION: AssetClass.OPTION,
            SecGroupValues.SWAP: AssetClass.EQUITY_SWAP,
            SecTypeValues.CDSWAP: AssetClass.CDS_INDEX,
            SecTypeValues.CSWAP: AssetClass.FX_SWAP,
            SecTypeValues.FWRD: AssetClass.FX_FORWARD,
            SecTypeValues.OPT: AssetClass.FX_OPTION,
            SecTypeValues.SPOT: AssetClass.FX_SPOT,
        }

        # Detect asset class through the security group
        result: pd.DataFrame = run_map_value(
            source_frame=self.source_frame.loc[
                :, [add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)]
            ],
            params=ParamsMapValue(
                source_attribute=add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
                ),
                target_attribute=DevColumns.ASSET_CLASS,
                case_insensitive=True,
                value_map=value_map,
            ),
            skip_serializer=True,
        )

        null_mask = result.loc[:, DevColumns.ASSET_CLASS].isnull()

        # Detect asset class through the security type
        if null_mask.any():
            result.loc[null_mask, DevColumns.ASSET_CLASS] = run_map_value(
                source_frame=self.source_frame.loc[
                    null_mask,
                    [add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)],
                ],
                params=ParamsMapValue(
                    source_attribute=add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE
                    ),
                    target_attribute=DevColumns.ASSET_CLASS,
                    case_insensitive=True,
                    value_map=value_map,
                ),
                skip_serializer=True,
            )[DevColumns.ASSET_CLASS]

        null_mask = result.loc[:, DevColumns.ASSET_CLASS].isnull()

        # Detect asset class of funds in Equity files
        if null_mask.any() and "_EQ" in self.source_file_uri.upper():
            equity_value_map = {
                SecGroupValues.FUND: AssetClass.EQUITY,
            }

            result.loc[null_mask, DevColumns.ASSET_CLASS] = run_map_value(
                source_frame=self.source_frame.loc[
                    null_mask,
                    [add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)],
                ],
                params=ParamsMapValue(
                    source_attribute=add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP
                    ),
                    target_attribute=DevColumns.ASSET_CLASS,
                    case_insensitive=True,
                    value_map=equity_value_map,
                ),
                skip_serializer=True,
            )[DevColumns.ASSET_CLASS]

        return result[DevColumns.ASSET_CLASS]

    def _get_client(self) -> pd.Series:
        """Returns a dataframe with col '__client__"""
        portfolio_id_with_prefix = PartyPrefix.ID + self.source_frame.loc[
            :,
            add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID),
        ].astype("string")

        return pd.DataFrame(
            data=portfolio_id_with_prefix.values,
            index=self.source_frame.index,
            columns=[DevColumns.CLIENT],
        )[DevColumns.CLIENT]

    def _get_trader(self):
        result: pd.Series = pd.Series(
            data=(
                PartyPrefix.ID
                + self.source_frame.loc[:, add_prefix(FileTypes.ORDER, OrderSourceColumns.TRADER)]
            ).values,
            name=DevColumns.TRADER,
            index=self.source_frame.index,
        )

        null_trader_mask = result.isnull()

        # handles edge case where the order id cache is empty
        if null_trader_mask.any() and self.order_id_cache_dict:
            # handles scenario where the order_id_cache does not have
            # a matching order ID, or the trader is missing
            # (result will remain the same = pd.NA or np.nan)
            result.loc[null_trader_mask] = (
                self.source_frame.loc[
                    null_trader_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                ]
                .astype("string")
                .map(self.order_id_cache_dict)
                .str.get("trader")
            )
            result.loc[null_trader_mask] = PartyPrefix.ID + result.loc[null_trader_mask].fillna(
                pd.NA
            )

        return result

    def _get_investment_decision_within_firm(self):
        return pd.Series(
            data=(
                PartyPrefix.ID
                + self.source_frame.loc[
                    :, add_prefix(FileTypes.ORDER, OrderSourceColumns.PM_INITIALS)
                ].astype("string")
            ).values,
            name=DevColumns.INVESTMENT_DECISION_WITHIN_FIRM,
            index=self.source_frame.index,
        )

    def _get_option_type(self) -> pd.Series:
        r"""
        transaction.[SECDESC1]. pattern = r'([CP]) @ ([\d\.]+)'
        e.g. "DEC24 ASML NA C @ 860.2962" would return 'C'
        """
        temp_result: pd.DataFrame = pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1)
            ]
            .astype("string")
            .str.extract(r"([CP]) @ [\d\.]+", expand=False)
            .values,
            index=self.pre_process_df.index,
            columns=[DevColumns.OPTION_TYPE],
        )
        value_map = {
            "C": OptionType.CALL.value,
            "P": OptionType.PUTO.value,
        }
        result: pd.DataFrame = run_map_value(
            source_frame=temp_result,
            params=ParamsMapValue(
                source_attribute=DevColumns.OPTION_TYPE,
                target_attribute=DevColumns.OPTION_TYPE,
                case_insensitive=True,
                value_map=value_map,
                default_value=OptionType.OTHR.value,
            ),
            skip_serializer=True,
        )
        # option_type must only be populated for options
        is_option_mask = self.pre_process_df.loc[:, DevColumns.ASSET_CLASS] == AssetClass.OPTION
        result.loc[~is_option_mask, DevColumns.OPTION_TYPE] = pd.NA

        return result[DevColumns.OPTION_TYPE]

    def _get_instrument_classification(self) -> pd.Series:
        sec_group_col = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
        sec_type_col = add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)

        result: pd.Series = run_map_conditional(
            source_frame=self.source_frame.loc[:, [sec_group_col, sec_type_col]],
            params=MapConditionalParams(
                target_attribute=DevColumns.INSTRUMENT_CLASSIFICATION,
                cases=[
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.LOAN}', case=False, na=False)",  # noqa: E501
                        value="LLXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.EQUITY}', case=False, na=False)",  # noqa: E501
                        value="ESXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.FUTURE}', case=False, na=False)",  # noqa: E501
                        value="FFXXXX",
                    ),
                    Case(
                        query=f"`{sec_type_col}`.str.fullmatch('{SecTypeValues.SPOT}', case=False, na=False)",  # noqa: E501
                        value="IFXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.SWAP}', case=False, na=False)",  # noqa: E501
                        value="SEXXXX",
                    ),
                    Case(
                        query=f"`{sec_type_col}`.str.fullmatch('{SecTypeValues.FWRD}', case=False, na=False)",  # noqa: E501
                        value="JXXXXX",
                    ),
                    Case(
                        query=f"`{sec_type_col}`.str.fullmatch('{SecTypeValues.OPT}', case=False, na=False)",  # noqa: E501
                        value="HFXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.CDS}', case=False, na=False)",  # noqa: E501
                        value="SCXXXX",
                    ),
                    Case(
                        query=f"`{sec_type_col}`.str.fullmatch('{SecTypeValues.CSWAP}', case=False, na=False)",  # noqa: E501
                        value="SFXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.OPTION}', case=False, na=False)",  # noqa: E501
                        value="OXXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.CMO}', case=False, na=False)",  # noqa: E501
                        value="DXXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.CMBS}', case=False, na=False)",  # noqa: E501
                        value="DXXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.ABS}', case=False, na=False)",  # noqa: E501
                        value="DAXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.BND}', case=False, na=False)",  # noqa: E501
                        value="DBXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.MBS}', case=False, na=False)",  # noqa: E501
                        value="DGXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.CASH}', case=False, na=False)",  # noqa: E501
                        value="DYXXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.FUND}', case=False, na=False) & "  # noqa: E501
                        f"`{sec_type_col}`.str.fullmatch('{SecTypeValues.CLOSED_END}', case=False, na=False)",  # noqa: E501
                        value="CIOXXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.IBND}', case=False, na=False) & "  # noqa: E501
                        f"`{sec_type_col}`.str.fullmatch('{SecTypeValues.GOVT}', case=False, na=False)",  # noqa: E501
                        value="DBXTXX",
                    ),
                    Case(
                        query=f"`{sec_group_col}`.str.fullmatch('{SecGroupValues.SYNTH}', case=False, na=False) & "  # noqa: E501
                        f"`{sec_type_col}`.str.fullmatch('{SecTypeValues.SWAPTION}', case=False, na=False)",  # noqa: E501
                        value="SCICXX",
                    ),
                ],
            ),
            skip_serializer=True,
        )[DevColumns.INSTRUMENT_CLASSIFICATION]
        return result

    def _get_expiry_date(self) -> pd.Series:
        result: pd.Series = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.MATURITY
                ),
                target_attribute=DevColumns.EXPIRY_DATE,
                source_attribute_format="%m/%d/%Y",
                convert_to=ConvertTo.DATE,
            ),
            skip_serializer=True,
        )[DevColumns.EXPIRY_DATE]
        return result

    def _get_option_strike_price(self) -> pd.Series:
        """
        WHEN secGroup = OPTION
            THEN
            transaction.[TRADECOUPON] (float datatype)
            If transaction.[TRADECOUPON] == 0 -> set to null
        """

        result = pd.Series(
            data=[pd.NA] * self.pre_process_df.shape[0],
            index=self.pre_process_df.index,
            name=DevColumns.STRIKE_PRICE,
        )

        is_option_mask = (
            self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
            ]
            .astype("string")
            .str.fullmatch(SecGroupValues.OPTION, case=False, na=False)
        )

        result[is_option_mask] = (
            self.source_frame.loc[
                is_option_mask,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.TRADE_COUPON),
            ]
            .fillna(0)
            .astype("float")
            .replace(0, pd.NA)
        )

        return result

    @staticmethod
    def _get_underlying_symbol_and_expiry_code(df: pd.DataFrame) -> pd.DataFrame:
        """
        This uses a utility function to extract the underlying symbol and expiry code
        from the SEC_TICKER column of the transaction file, converting the BBG venues
        to exchanges expected by SteelEye.

        If option:   transaction.[SECTICKER].split(' ')[0]
        e.g. "HPQ US OTC" would return 'HPQ'

        If future: transaction.[SECTICKER][:-2]
        e.g. 'MESZ1' would return 'MES'
        """
        replacements_result: pd.DataFrame = underlying_symbol_and_expiry_code_replacements(
            dataframe=df,
            symbol_column=add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER),
            venue_column=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            asset_class_column=DevColumns.ASSET_CLASS,
            symbol_and_expiry_code_target_column=DevColumns.SYMBOL_AND_EXPIRY_CODE,
            symbol_only_target_column=DevColumns.SYMBOL,
        )
        # full match
        replacements_result[DevColumns.SYMBOL] = replacements_result[DevColumns.SYMBOL].replace(
            "JGS", "NIFTY"
        )
        # partial match
        not_null_mask = replacements_result[DevColumns.SYMBOL_AND_EXPIRY_CODE].notnull()
        if not_null_mask.any():
            replacements_result.loc[not_null_mask, DevColumns.SYMBOL_AND_EXPIRY_CODE] = (
                replacements_result.loc[not_null_mask, DevColumns.SYMBOL_AND_EXPIRY_CODE]
                .astype("string")
                .str.replace("JGS", "NIFTY")
            )

        return replacements_result

    def _get_notional_currency_2(self) -> pd.Series:
        """
        ConvertMinorToMajor is being used to map the CNH -> CNY currencies
        NOTE: The prices are not being converted at the minor-major rate.

        FOR FX Transaction.fxTermCcy
        """
        # If the SEC_DESC_1 contains exactly one "/", this will return the text after it.
        # It is not expected for the SEC_DESC_1 to contain more than one "/".
        # If there is no "/" in the SEC_DESC_1, this will return np.nan.
        if self.file_type_asset_class == FileTypeAssetClass.FX:
            result: pd.DataFrame = run_convert_minor_to_major(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.FX_TERM_CCY
                    ),
                    target_ccy_attribute=DevColumns.NOTIONAL_CURRENCY_2,
                ),
                skip_serializer=True,
            )
            return result[DevColumns.NOTIONAL_CURRENCY_2]

        temp_notional_currency_2_df = (
            self.source_frame.loc[
                :,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1),
            ]
            .astype("string")
            .str.split("/")
            .str[1]
        ).to_frame()

        return run_convert_minor_to_major(
            source_frame=temp_notional_currency_2_df,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.SEC_DESC_1
                ),
                target_ccy_attribute=DevColumns.NOTIONAL_CURRENCY_2,
            ),
            skip_serializer=True,
        ).loc[:, DevColumns.NOTIONAL_CURRENCY_2]

    def _get_newo_in_file(self) -> pd.Series:
        """The input files never provide explicit NEWOs.

        They provide "unknown" order events, for which we will only
        create a synth NEWO OR create a synth NEWO + a PARF/CAME OR only
        create a PARF/CAME.
        """
        cases = [
            {
                "query": "index==index",
                "value": False,
            },
        ]
        result: pd.Series = run_map_conditional(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=DevColumns.NEWO_IN_FILE,
                cases=cases,
            ),
            skip_serializer=True,
        )[DevColumns.NEWO_IN_FILE]
        return result

    def _date(self):
        pass

    def _execution_details_additional_limit_price(self):
        pass

    def _execution_details_aggregated_order(self):
        pass

    def _execution_details_client_additional_info(self):
        pass

    def _execution_details_liquidity_provision_activity(self):
        pass

    def _execution_details_mes_first_execution_only(self):
        pass

    def _execution_details_min_acceptable_quantity(self):
        pass

    def _execution_details_min_executable_size(self):
        pass

    def _execution_details_order_restriction(self):
        pass

    def _execution_details_passive_aggressive_indicator(self):
        pass

    def _execution_details_passive_only_indicator(self):
        pass

    def _execution_details_pegged_limit_price(self):
        pass

    def _execution_details_routing_strategy(self):
        pass

    def _execution_details_securities_financing_txn_indicator(self):
        pass

    def _execution_details_self_execution_prevention(self):
        pass

    def _execution_details_settlement_amount(self):
        pass

    def _execution_details_short_selling_indicator(self):
        pass

    def _execution_details_trading_capacity(self):
        pass

    def _execution_details_waiver_indicator(self):
        pass

    def _financing_type(self):
        pass

    def _hierarchy(self):
        pass

    def _is_iceberg(self):
        pass

    def _is_discretionary(self):
        pass

    def _jurisdiction_business_line(self):
        pass

    def _jurisdiction_legal_entity(self):
        pass

    def _jurisdiction_country(self):
        pass

    def _mar_details_is_personal_account_dealing(self):
        pass

    def _multi_leg_reporting_type(self):
        pass

    def _order_identifiers_order_id_code(self):
        pass

    def _order_identifiers_sequence_number(self):
        pass

    def _order_identifiers_trading_venue_order_id_code(self):
        pass

    def _order_identifiers_transaction_ref_no(self):
        pass

    def _price_forming_data_display_quantity(self):
        pass

    def _price_forming_data_initial_quantity(self):
        pass

    def _price_forming_data_modified_quantity(self):
        pass

    def _price_forming_data_price(self):
        pass

    def _price_forming_data_price_not_applicable(self):
        pass

    def _price_forming_data_price_pending(self):
        pass

    def _price_forming_data_remaining_quantity(self):
        pass

    def _price_forming_data_traded_quantity(self):
        pass

    def _source_index(self):
        """This method must be implemented in the child Client/Market side
        classes."""
        pass

    def _timestamps_external_order_received(self):
        pass

    def _timestamps_external_order_submitted(self):
        pass

    def _timestamps_internal_order_received(self):
        pass

    def _timestamps_internal_order_submitted(self):
        pass

    def _timestamps_order_received(self):
        pass

    def _timestamps_order_status_updated(self):
        pass

    def _timestamps_trading_date_time(self):
        pass

    def _timestamps_validity_period(self):
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ):
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ):
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self):
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(self):
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self):
        pass

    def _transaction_details_branch_membership_country(self):
        pass

    def _transaction_details_commission_amount(self):
        pass

    def _transaction_details_commission_amount_currency(self):
        pass

    def _transaction_details_commission_amount_type(self):
        pass

    def _transaction_details_cross_indicator(self):
        pass

    def _transaction_details_cumulative_quantity(self):
        pass

    def _transaction_details_derivative_notional_change(self):
        pass

    def _transaction_details_net_amount(self):
        pass

    def _transaction_details_order_id_code(self):
        pass

    def _transaction_details_outgoing_order_addl_info(self):
        pass

    def _transaction_details_position_effect(self):
        pass

    def _transaction_details_position_id(self):
        pass

    def _transaction_details_price(self):
        pass

    def _transaction_details_price_average(self):
        pass

    def _transaction_details_price_pending(self):
        pass

    def _transaction_details_pricing_details_percent_vs_market(self):
        pass

    def _transaction_details_quantity(self):
        pass

    def _transaction_details_record_type(self):
        pass

    def _transaction_details_settlement_amount(self):
        pass

    def _transaction_details_settlement_amount_currency(self):
        pass

    def _transaction_details_settlement_date(self):
        pass

    def _transaction_details_settlement_type(self):
        pass

    def _transaction_details_swap_directionalities(self):
        pass

    def _transaction_details_trading_capacity(self):
        pass

    def _transaction_details_trading_date_time(self):
        pass

    def _transaction_details_trail_id(self):
        pass

    def _transaction_details_upfront_payment(self):
        pass

    def _transaction_details_upfront_payment_currency(self):
        pass

    def _transaction_details_venue(self):
        pass

    def _is_repo(self):
        pass

    def _order_class(self) -> pd.DataFrame:
        pass

    def _build_is_otc_mask(self) -> pd.Series:
        null_orig_order_id = self.source_frame[
            add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID)
        ].isnull()

        order_status_f_mask = (
            self.source_frame[add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS)]
            .astype("str")
            .str.fullmatch("F", case=False, na=False)
        )

        null_owner_type_mask = self.source_frame[
            add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE)
        ].isnull()

        is_otc_mask = null_orig_order_id & order_status_f_mask & null_owner_type_mask
        return is_otc_mask

    @staticmethod
    def _get_file_type_asset_class(file_uri: str) -> str:
        return re.match(ALADDIN_FILES_PATTERN, Path(file_uri).stem).group("asset_class")  # type: ignore[union-attr]

    @staticmethod
    def skip_records_by_asset_class(
        source_frame: pd.DataFrame,
        input_file_type_asset_class: str,
        target_file_type_asset_class: str,
        sec_group_and_type_skip_list: List[Tuple[str, str]],
    ) -> pd.DataFrame:
        # Mark has requested for this skip logic to not be audited
        if input_file_type_asset_class == target_file_type_asset_class:
            for skip_combination in sec_group_and_type_skip_list:
                sec_group_skip_mask = (
                    source_frame.loc[
                        :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_GROUP)
                    ]
                    .astype("string")
                    .str.fullmatch(skip_combination[0], case=False, na=False)
                )
                sec_type_skip_mask = (
                    source_frame.loc[
                        :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TYPE)
                    ]
                    .astype("string")
                    .str.fullmatch(skip_combination[1], case=False, na=False)
                )

                skip_mask = sec_group_skip_mask & sec_type_skip_mask
                source_frame = source_frame.loc[~skip_mask, :]

        return source_frame
