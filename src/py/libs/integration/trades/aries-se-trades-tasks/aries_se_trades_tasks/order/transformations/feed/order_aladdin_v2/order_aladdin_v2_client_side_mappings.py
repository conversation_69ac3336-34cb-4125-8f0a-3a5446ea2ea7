import pandas as pd
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    run_convert_datetime,
)
from aries_se_core_tasks.transform.map.map_value import run_map_value
from aries_se_core_tasks.utilities.elasticsearch_utils import es_scroll, get_terms_query
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_base_mappings import (  # noqa: E501
    OrderAladdinV2BaseMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    DATE_TIME_FORMAT,
    DevColumns,
    FileTypes,
    OrderDetailSourceColumns,
    OrderSourceColumns,
    PlacementSourceColumns,
    TransactionSourceColumns,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.datetime.convert_datetime import Params as ParamsConvertDatetime
from se_core_tasks.map.map_value import Params as ParamsMapValue
from se_elastic_schema.models import Order
from se_elastic_schema.static.mifid2 import OrderStatus, TradingCapacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.order.static import (
    ModelPrefix,
    OrderColumns,
    Venue,
    add_prefix,
)
from se_trades_tasks.order_and_tr.static import AssetClass


class OrderAladdinV2ClientSideMappings(OrderAladdinV2BaseMappings):
    def _date(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.AUTHORIZED_TIMESTAMP_UTC
                ),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
            skip_serializer=True,
        )

        if self.is_otc_mask.any():
            result.loc[self.is_otc_mask, OrderColumns.DATE] = run_convert_datetime(
                self.source_frame,
                params=ParamsConvertDatetime(
                    source_attribute=add_prefix(
                        FileTypes.ORDER, OrderSourceColumns.CREATED_TIMESTAMP_UTC
                    ),
                    source_attribute_format=DATE_TIME_FORMAT,
                    target_attribute=OrderColumns.DATE,
                    convert_to=ConvertTo.DATE,
                ),
                skip_serializer=True,
            )[OrderColumns.DATE]

        return result

    def _execution_details_order_status(self) -> pd.DataFrame:
        """This method is quite complex and is used not only to determine the
        Order Status of an Order, but also to populate certain data points that
        may be used in other mappings, such as to calculate the traded
        quantity.

        Unconditionally, we attempt to create a synthetic NEWO per input record.
        If the synth NEWO is a duplicate, it will be dropped downstream in the flow's
        RemoveDuplicateNEWO Task.

        By default, we attempt to additionally create a PARF Order per input record.

        If `orderDetail.orderDetailQuantityBooked` is null or 0, it means that an Order
        was placed, but it wasn't yet filled or cancelled, hence, we cannot create a PARF for it.
        In this case, we set the Order Status of the "OrderState" to null, so that it gets
        dropped downstream.

        If input Order Status = C AND `orderDetail.orderDetailQuantityBooked` = 0 AND
        `orderDetail.orderDetailQuantity` = 0, it means that the Order was cancelled,
        without ever having been partially-filled, hence a full cancellation. The resulting
        order status is CAME, and it does not require any additional logic.

        If input Order Status = F AND `orderDetail.orderDetailQuantity` = `orderDetail.orderDetailQuantityBooked`,
        AND `orderDetail.orderDetailQuantity` is less than the previous event's `orderDetail.orderDetailQuantity`,
        then it means that we are dealing with an Order that was cancelled, despite having been partially-filled,
        hence a partially-filled cancellation. The resulting order status is CAME, and the difference between
        the "new" initial quantity and the "old" initial quantity is the actual traded quantity. The
        `orderDetail.orderDetailQuantity` is exactly how much was cancelled.

        This difference is stored in a class instance variable to be used in the
        `priceFormingData.tradedQuantity` mapping.
        """  # noqa: E501

        # By default, we create a PARF per input OrderDetail
        parfs_df = pd.DataFrame(
            data=OrderStatus.PARF.value,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                )
            ],
        )

        # If orderDetail.orderDetailQuantityBooked is null or 0, we must NOT create a PARF.
        # To achieve this, we set the executionDetails.orderStatus value to pd.NA.
        # Downstream on the flow, we will discard all rows where the orderStatus
        # is null, fulfilling the spec's logic to only create NEWOs in this scenario
        quantity_booked_is_zero_or_null_mask = (
            self.source_frame.loc[
                :,
                add_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED
                ),
            ]
            .fillna(0.0)
            .astype("float")
            == 0.0
        )
        parfs_df.loc[
            quantity_booked_is_zero_or_null_mask,
            add_prefix(
                ModelPrefix.ORDER_STATE,
                OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
            ),
        ] = pd.NA

        # ClientSide Cancel Whole Order
        order_detail_quantity_is_zero_mask = (
            self.source_frame.loc[
                :,
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY),
            ]
            .fillna(1.0)
            .astype("float")
            == 0.0
        )
        quantity_booked_is_zero_mask = (
            self.source_frame.loc[
                :,
                add_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED
                ),
            ]
            .fillna(1.0)
            .astype("float")
            == 0.0
        )
        c_order_status_mask = (
            self.source_frame.loc[
                :,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS),
            ].astype("string")
            == "C"
        )
        combined_mask = (
            order_detail_quantity_is_zero_mask & quantity_booked_is_zero_mask & c_order_status_mask
        )

        parfs_df.loc[
            combined_mask,
            add_prefix(
                ModelPrefix.ORDER_STATE,
                OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
            ),
        ] = OrderStatus.CAME.value

        # Cancel part filled order
        # arbitrary .fillna() value to ensure one column
        # does not clash with the other in case only of them is null
        order_detail_quantity_and_booked_quantity_is_the_same_mask = self.source_frame.loc[
            :,
            add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY),
        ].fillna(1.12345).astype("float") == self.source_frame.loc[
            :,
            add_prefix(
                FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED
            ),
        ].fillna(2.12345).astype("float")

        f_order_status_mask = (
            self.source_frame.loc[
                :,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS),
            ].astype("string")
            == "F"
        )
        preliminary_combined_mask = (
            order_detail_quantity_and_booked_quantity_is_the_same_mask & f_order_status_mask
        )

        if preliminary_combined_mask.any():
            order_ids = list(
                set(
                    self.target_df.loc[
                        preliminary_combined_mask,
                        add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID),
                    ].values
                )
            )

            query = get_terms_query(
                ids=order_ids,
                es_client=self.es_client,
                lookup_field=OrderColumns.ID,
                model_field=Order.get_reference().name,
                source_field=[
                    OrderColumns.ID,
                    OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                    OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                ],
            )

            elastic_result_df: pd.DataFrame = es_scroll(
                es_client=self.es_client,
                index_alias=Order.get_elastic_index_alias(tenant=self.tenant),
                query=query,
            )

            if elastic_result_df.empty:
                # If there are no previous order events for these supposed
                # part-filled cancellations, then they aren't cancellations after all;
                # thus we keep the default PARF order status
                pass

            else:
                elastic_result_df = (
                    elastic_result_df.dropna(
                        subset=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED], axis=0
                    )
                    .sort_values(by=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED], ascending=False)
                    .drop_duplicates(subset=[OrderColumns.ID], keep="first")
                    .drop([OrderColumns.TIMESTAMPS_ORDER_RECEIVED], axis=1)
                )

                # when merging the 2 dataframes, the initialQuantity column will be present twice:
                # once from the left DF and another one from the right DF.
                # the left df represents the new initial quantity,
                # and it will have the suffix "_source".
                # the right df represents the old initial quantity,
                # and it will have the suffix "_elastic"
                new_vs_old_quantity_df = (
                    self.target_df.loc[
                        :,
                        [
                            add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID),
                            OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                        ],
                    ]
                    .rename(
                        columns={
                            add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID): OrderColumns.ID
                        }
                    )
                    .reset_index()
                    .merge(
                        elastic_result_df,
                        on=[OrderColumns.ID],
                        how="left",
                        suffixes=("_source", "_elastic"),
                    )
                    .set_index(self.target_df.index.name)
                )

                new_quantity_is_less_than_old_quantity_mask = new_vs_old_quantity_df.loc[
                    :,
                    OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY + "_source",
                ].fillna(0) < new_vs_old_quantity_df.loc[
                    :,
                    OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY + "_elastic",
                ].fillna(0)

                final_combined_mask = (
                    preliminary_combined_mask & new_quantity_is_less_than_old_quantity_mask
                )

                # All orders that have been part-filled and have a new initial quantity less
                # than the old initial quantity are considered to be cancellations
                parfs_df.loc[
                    final_combined_mask,
                    add_prefix(
                        ModelPrefix.ORDER_STATE,
                        OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                    ),
                ] = OrderStatus.CAME.value

                # Store the quantity difference between the new and old initial quantities
                # for cancelled orders, to re-use in the priceFormingData.tradedQuantity mapping
                quantity_diff_series = (
                    new_vs_old_quantity_df.loc[
                        final_combined_mask,
                        OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY + "_source",
                    ].fillna(0)
                    - new_vs_old_quantity_df.loc[
                        final_combined_mask,
                        OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY + "_elastic",
                    ].fillna(0)
                ).abs()
                quantity_diff_series.name = "quantity_diff_series"

                quantity_diff_by_order_id_df = pd.concat(
                    [
                        self.target_df.loc[
                            final_combined_mask,
                            add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID),
                        ],
                        quantity_diff_series.to_frame(),
                    ],
                    axis=1,
                )

                self.cancelled_quantity_diff_by_order_id_map = dict(
                    zip(
                        quantity_diff_by_order_id_df[
                            add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)
                        ],
                        quantity_diff_by_order_id_df["quantity_diff_series"],
                    )
                )

        newos_df = pd.DataFrame(
            data=[OrderStatus.NEWO.value] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS)],
        )

        if self.is_otc_mask.any():
            # OTCs are always FILLs
            parfs_df.loc[
                self.is_otc_mask,
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS),
            ] = OrderStatus.FILL.value

        return pd.concat(
            [
                newos_df,
                parfs_df,
            ],
            axis=1,
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        value_map = {
            "A": TradingCapacity.AOTC.value,
            "O": TradingCapacity.AOTC.value,
            "P": TradingCapacity.DEAL.value,
            "R": TradingCapacity.AOTC.value,
            "M": TradingCapacity.AOTC.value,
            "I": TradingCapacity.DEAL.value,
            "S": TradingCapacity.DEAL.value,
            "L": TradingCapacity.DEAL.value,
            "C": TradingCapacity.DEAL.value,
        }
        return run_map_value(
            source_frame=self.source_frame.loc[
                :, [add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.DEALING_CAPACITY)]
            ],
            params=ParamsMapValue(
                source_attribute=add_prefix(
                    FileTypes.TRANSACTION, TransactionSourceColumns.DEALING_CAPACITY
                ),
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                case_insensitive=True,
                value_map=value_map,
                default_value=TradingCapacity.AOTC.value,
            ),
            skip_serializer=True,
        )

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _id(self) -> pd.DataFrame:
        result_series = (
            "C|"
            + self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID)
            ]
            + "_"
            + self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID)
            ]
            + "_"
            + self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID)
            ]
        )

        if self.is_otc_mask.any():
            result_series[self.is_otc_mask] = (
                "C|"
                + self.source_frame.loc[
                    self.is_otc_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                ]
                + "_"
                + self.source_frame.loc[
                    self.is_otc_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID),
                ]
            )
            # See https://steeleye.atlassian.net/browse/ON-5224?focusedCommentId=211435
            # for more details. We expect to see trades without an `orderDetail.origOrderId`,
            # which have the same`orderDetail.orderId` and `orderDetail.portfolioId`,
            # but potentially different FILLs. We want to ensure that we
            # create separate NEWOs for these trades.
            duplicated_id_mask = result_series.duplicated(
                keep=False
            )  # marks all duplicate rows as True
            if duplicated_id_mask.any():
                result_series[duplicated_id_mask] += (
                    "_"
                    + self.source_frame.loc[
                        duplicated_id_mask,
                        add_prefix(FileTypes.ORDER_DETAILS, "__swarm_raw_index__"),
                    ]
                )

        return pd.concat(
            [
                pd.DataFrame(
                    data=result_series.values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                ),
                pd.DataFrame(
                    data=result_series.values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
            ],
            axis=1,
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, add_prefix(ModelPrefix.ORDER, OrderColumns.ID)].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        result_series = (
            self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID)
            ]
            + "|"
            + self.target_df.loc[:, OrderColumns.SOURCE_INDEX].astype("string")
        )

        if self.is_otc_mask.any():
            result_series[self.is_otc_mask] = (
                self.source_frame.loc[
                    self.is_otc_mask,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                ]
                + "|"
                + self.target_df.loc[self.is_otc_mask, OrderColumns.SOURCE_INDEX].astype("string")
            )

        return pd.concat(
            [
                pd.DataFrame(
                    data=result_series.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        )
                    ],
                ),
                pd.DataFrame(
                    data=result_series.values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            ModelPrefix.ORDER_STATE, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(ModelPrefix.ORDER, OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO),
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=(
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY
                    ),
                ]
            ).values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        ).abs()

    def _price_forming_data_price(self) -> pd.DataFrame:
        result: pd.DataFrame = pd.DataFrame(
            data=(
                self.source_frame.loc[:, add_prefix(FileTypes.ORDER, OrderSourceColumns.AVG_PRICE)]
            ).values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_PRICE],
        )

        # Inherit the average price from the Order cache by merged order id.
        # If the average price is not available in the cache, it will remain unchanged.
        if self.order_id_cache_dict:
            result.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE] = (
                (
                    self.source_frame.loc[
                        :,
                        add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                    ]
                    .astype("string")
                    .map(self.order_id_cache_dict)
                    .str.get("avg_price")
                )
                .astype("float")
                .fillna(result.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE])
            )

        return result

    def _transaction_details_price(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Calculates the traded quantity for each order.

        For PARFs, the default traded quantity is the `orderDetailQuantityBooked`
        value. This is because the `orderDetailQuantityBooked` value is the cumulative-booked
        quantity for each order. If we are processing the first order event
        within the order lifecycle, then the cumulative traded quantity is indeed
        the traded quantity of that event. That is not true any more for subsequent
        order events.

        For the subsequent order events, we need to calculate the traded quantity
        by subtracting the `orderDetailQuantityBooked` of the previous order event
        from the `orderDetailQuantityBooked` of the current order event.
        That is why we map the input `orderDetailQuantityBooked` input field against the
        `orderIdentifiers.sequenceNumber` field of the output records. This ensures
        that for very Order, we know the cumulative traded quantity up until that moment.

        For CAMEs, we will re-use the dict populated in `_executionDetails.orderStatus`
        as we have already calculated the diff in initialQuantity between the order
        that we are processing and the previous order event.
        """

        # Start logic to calculate traded quantity on PARFs
        target_prefix = add_prefix(
            ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
        )

        traded_quantity_df = pd.DataFrame(
            data=(
                self.source_frame.loc[
                    :,
                    add_prefix(
                        FileTypes.ORDER_DETAILS,
                        OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED,
                    ),
                ]
            ).values,
            index=self.source_frame.index,
            columns=[target_prefix],
        ).abs()
        traded_quantity_df.loc[:, OrderColumns.ID] = self.target_df.loc[
            :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)
        ]

        order_ids = list(
            set(self.target_df.loc[:, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)].values)
        )

        query = get_terms_query(
            ids=order_ids,
            es_client=self.es_client,
            lookup_field=OrderColumns.ID,
            model_field=Order.get_reference().name,
            source_field=[
                OrderColumns.ID,
                OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER,
            ],
        )

        elastic_result_df: pd.DataFrame = es_scroll(
            es_client=self.es_client,
            index_alias=Order.get_elastic_index_alias(tenant=self.tenant),
            query=query,
        )

        if elastic_result_df.empty:
            return traded_quantity_df.loc[:, [target_prefix]]

        elastic_result_df = (
            elastic_result_df.dropna(
                subset=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME], axis=0
            )
            .sort_values(by=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME], ascending=False)
            .drop_duplicates(subset=[OrderColumns.ID], keep="first")
            .drop([OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME], axis=1)
        )

        traded_quantity_df = (
            traded_quantity_df.reset_index()
            .merge(elastic_result_df, on=[OrderColumns.ID], how="left")
            .set_index(traded_quantity_df.index.name)
        )

        traded_quantity_df.loc[:, target_prefix] = traded_quantity_df.loc[
            :, target_prefix
        ] - traded_quantity_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER].fillna(0)

        # End logic to calculate traded quantity on PARFs

        # Start logic to calculate canceled quantity on CAMEs

        cancel_mask = (
            self.target_df.loc[
                :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS)
            ]
            == OrderStatus.CAME.value
        )

        # For part-filled cancellations, we need to subtract the quantity difference
        # between the new and old initial quantities
        # i.e. if an Order was initially placed with an initial quantity of 10 000
        # but on the following day we receive a CAME with an
        # initial quantity of 2000 for the same order ID
        # this means that the traded/cancelled quantity was 10 000 - 2000 = 8000

        # For whole order cancellations, the
        # `self.cancelled_quantity_diff_by_order_id_map` will not contain
        # a value for that order ID, hence the traded quantity will be set to pd.NA.
        # This is intentional.
        traded_quantity_df.loc[cancel_mask, target_prefix] = traded_quantity_df.loc[
            cancel_mask, OrderColumns.ID
        ].map(self.cancelled_quantity_diff_by_order_id_map)

        # End logic to calculate canceled quantity on CAMEs

        return traded_quantity_df.loc[:, [target_prefix]]

    def _transaction_details_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=(
                self.target_df.loc[
                    :,
                    add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
                    ),
                ]
            ).values,
            index=self.source_frame.index,
            columns=[
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_QUANTITY)
            ],
        )

    def _source_index(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, add_prefix(FileTypes.ORDER_DETAILS, "__swarm_raw_index__")
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_datetime(
            self.source_frame.loc[
                :, [add_prefix(FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC)]
            ],
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.ACTIVATED_TIMESTAMP_UTC
                ),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )

        null_result_mask = result.loc[:, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED].isnull()
        if null_result_mask.any():
            result.loc[null_result_mask, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED] = (
                run_convert_datetime(
                    self.source_frame.loc[
                        null_result_mask,
                        [add_prefix(FileTypes.ORDER, OrderSourceColumns.AUTHORIZED_TIMESTAMP_UTC)],
                    ],
                    params=ParamsConvertDatetime(
                        source_attribute=add_prefix(
                            FileTypes.ORDER, OrderSourceColumns.AUTHORIZED_TIMESTAMP_UTC
                        ),
                        source_attribute_format=DATE_TIME_FORMAT,
                        target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                        convert_to=ConvertTo.DATETIME,
                    ),
                    skip_serializer=True,
                )[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED]
            )

        if self.is_otc_mask.any():
            result.loc[self.is_otc_mask, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED] = (
                run_convert_datetime(
                    self.source_frame.loc[
                        self.is_otc_mask,
                        [
                            add_prefix(
                                FileTypes.TRANSACTION,
                                TransactionSourceColumns.AUTHORIZED_TIMESTAMP_UTC,
                            )
                        ],
                    ],
                    params=ParamsConvertDatetime(
                        source_attribute=add_prefix(
                            FileTypes.TRANSACTION, TransactionSourceColumns.AUTHORIZED_TIMESTAMP_UTC
                        ),
                        source_attribute_format=DATE_TIME_FORMAT,
                        target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                        convert_to=ConvertTo.DATETIME,
                    ),
                    skip_serializer=True,
                )[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED]
            )

        return result

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_INTERNAL_ORDER_SUBMITTED],
        )

    def _timestamps_order_received(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.ORDER, OrderSourceColumns.AUTHORIZED_TIMESTAMP_UTC
                ),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )

        if self.is_otc_mask.any():
            result.loc[self.is_otc_mask, OrderColumns.TIMESTAMPS_ORDER_RECEIVED] = (
                run_convert_datetime(
                    self.source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute=add_prefix(
                            FileTypes.ORDER, OrderSourceColumns.CREATED_TIMESTAMP_UTC
                        ),
                        source_attribute_format=DATE_TIME_FORMAT,
                        target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                        convert_to=ConvertTo.DATETIME,
                    ),
                    skip_serializer=True,
                )[OrderColumns.TIMESTAMPS_ORDER_RECEIVED]
            )

        return result

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_INTERNAL_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        newo_order_status_updated = pd.DataFrame(
            data=pd.NA,  # type: ignore[call-overload]
            index=self.target_df.index,
            columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)],
        )

        order_state_order_status_updated: pd.DataFrame = run_convert_datetime(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=add_prefix(
                    FileTypes.PLACEMENT, PlacementSourceColumns.FINISH_TIME_UTC
                ),
                source_attribute_format=DATE_TIME_FORMAT,
                target_attribute=add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
                ),
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )
        order_state_order_status_updated_null_mask = order_state_order_status_updated.loc[
            :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)
        ].isnull()

        if order_state_order_status_updated_null_mask.any():
            order_state_order_status_updated.loc[
                order_state_order_status_updated_null_mask,
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED),
            ] = run_convert_datetime(
                self.source_frame.loc[order_state_order_status_updated_null_mask, :],
                params=ParamsConvertDatetime(
                    source_attribute=add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.CREATED_TIMESTAMP_UTC
                    ),
                    source_attribute_format=DATE_TIME_FORMAT,
                    target_attribute=add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
                    ),
                    convert_to=ConvertTo.DATETIME,
                ),
                skip_serializer=True,
            )[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)]

        if self.is_otc_mask.any():
            order_state_order_status_updated.loc[
                self.is_otc_mask,
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED),
            ] = run_convert_datetime(
                self.source_frame.loc[self.is_otc_mask, :],
                params=ParamsConvertDatetime(
                    source_attribute=add_prefix(
                        FileTypes.TRANSACTION, TransactionSourceColumns.MODIFIED_TIMESTAMP_UTC
                    ),
                    source_attribute_format=DATE_TIME_FORMAT,
                    target_attribute=add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
                    ),
                    convert_to=ConvertTo.DATETIME,
                ),
                skip_serializer=True,
            )[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)]

        return pd.concat([newo_order_status_updated, order_state_order_status_updated], axis=1)

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[
                :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME
                )
            ],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[
                :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED)
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME)
            ],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=OrderRecordType.ALLOCATION.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        # Default value is XOFF
        result: pd.DataFrame = pd.DataFrame(
            data=Venue.XOFF,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

        # Inherit the venue from the associated market-side order
        # by orderDetails.orderId. Preserve default value if the cache
        # does not have a non-null venue.
        if self.order_id_cache_dict:
            result.loc[:, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE] = (
                self.source_frame.loc[
                    :,
                    add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID),
                ]
                .astype("string")
                .map(self.order_id_cache_dict)
                .str.get("exchange")
            ).fillna(Venue.XOFF)

        # Map EUX to XEUR
        # Map 11 or GIFT to INSE
        value_map = {
            "EUX": "XEUR",
            "11": "INSE",
            "GIFT": "INSE",
        }
        result = run_map_value(
            source_frame=result,
            params=ParamsMapValue(
                source_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                case_insensitive=True,
                value_map=value_map,
                preserve_original=True,
            ),
            skip_serializer=True,
        )

        # Override venue to "OPRA" for options
        is_option_mask = self.pre_process_df.loc[:, DevColumns.ASSET_CLASS] == AssetClass.OPTION
        result.loc[is_option_mask, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE] = "OPRA"

        return result

    def _transaction_details_venue(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=Venue.XOFF,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _order_class(self):
        """
        Hardcode: “iCross”
        when Transaction.EXECCPTYTYPE = ICROSS

        Hardcode: “OTC”
        when OTC
        """
        result = pd.Series(
            data=[pd.NA] * self.target_df.shape[0],
            index=self.target_df.index,
            name=OrderColumns.ORDER_CLASS,
        )
        is_icross_mask = (
            self.source_frame.loc[
                :, add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.EXEC_CPCY_TYPE)
            ]
            .astype("string")
            .str.fullmatch("ICROSS", case=False, na=False)
        )
        result[is_icross_mask] = "iCross"

        if self.is_otc_mask.any():
            sec_ticker_is_otc_mask = (
                self.source_frame[
                    add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER)
                ]
                .astype("str")
                .str.fullmatch(".*OTC.*", case=False, na=False)
            )
            combined_mask = self.is_otc_mask & sec_ticker_is_otc_mask

            result[combined_mask] = "OTC"

        return result

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """This is a little cheating from our side. We need the
        orderDetailQuantityBooked value in each Order to be able to calculate
        the tradedQuantity of the next Order if it exists.

        The reason for this is that Aladdin does not provide
        the traded quantity of client-side orders.
        Instead, they provide the cumulative order booked quantity for each order. Meaning that
        the Traded Quantity of Order T is its cumulative-booked quantity - the cumulative booked
        quantity of Order T-X where X is an indefinite number of days.

        Hence, we store the cumulative-booked quantity of each order in any INT field
        which is not used anywhere.
        That is the case for the sequenceNumber field. This field is rendered in the application,
        but it is not used
        by any data consumers.
        """
        return pd.DataFrame(
            data=self.source_frame.loc[
                :,
                add_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED
                ),
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER],
        ).abs()

    def _get_buy_sell(self) -> pd.Series:
        result: pd.Series = super()._get_buy_sell()
        null_mask = result.isnull()
        if null_mask.any():
            # If we cannot map the buySell, we fall back on the quantity:
            # - if it is positive -> it is a buy (1)
            # - if it is negative -> it is a sell (2)
            result.loc[null_mask] = (
                self.source_frame.loc[
                    null_mask,
                    add_prefix(
                        FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY
                    ),
                ]
                .astype("float")
                .apply(
                    lambda x: pd.NA if pd.isnull(x) else "1" if x > 0 else "2",
                )
            )

        return result
